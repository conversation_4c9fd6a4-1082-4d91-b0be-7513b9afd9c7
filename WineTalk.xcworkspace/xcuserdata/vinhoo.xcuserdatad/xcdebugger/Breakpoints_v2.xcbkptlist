<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "5E74CCDE-1F92-41BE-A4B3-35CE90C1CB83"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "58797115-429A-4018-8EE5-4BB2700B5B37"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/WXApi/WXAuth.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "62"
            endingLineNumber = "62"
            landmarkName = "-handleOpenURL:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B5B013E5-1824-45AC-A46F-611135D492F3"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x89c6;&#x9891;/VideoReleasedViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "611"
            endingLineNumber = "611"
            landmarkName = "-finishButtonClicked"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "42B906C1-1FDA-4719-873D-67A96A0F010E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x89c6;&#x9891;/Alivc/AlivcEdit/Classes/Export/VC/AlivcExportViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "296"
            endingLineNumber = "296"
            landmarkName = "-pickButtonClicked"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "CE0B8CD8-8846-4F60-A50A-BA219BEBEF7C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x89c6;&#x9891;/Alivc/AlivcEdit/Classes/Export/VC/AlivcExportViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "120"
            endingLineNumber = "120"
            landmarkName = "-setupSubviews"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3C82DE43-0C87-4D10-BFA8-EC99133835A2"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Vender/XHButtomViews/FaceView/ZBFaceView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "95"
            endingLineNumber = "95"
            landmarkName = "-faceClick:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D9FE0D03-E8F3-43D5-ADD6-60A11C45715F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Vender/XHButtomViews/FaceView/WineToolView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "88"
            endingLineNumber = "88"
            landmarkName = "-faceClick:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "683061B1-62AA-45FC-BC80-2846CFD9751C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/TakingPicturesWine(&#x62cd;&#x7167;&#x8bc6;&#x9152;)/LimCamera(&#x76f8;&#x673a;&#x8bc6;&#x522b;&#x9152;)/LimCamera/DBUploadImgViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "306"
            endingLineNumber = "306"
            landmarkName = "-uploadImage:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4926EADD-5BBB-40DD-A00C-B354A055D2A4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/TakingPicturesWine(&#x62cd;&#x7167;&#x8bc6;&#x9152;)/LimCamera(&#x76f8;&#x673a;&#x8bc6;&#x522b;&#x9152;)/LimCamera/DBUploadImgViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "317"
            endingLineNumber = "317"
            landmarkName = "-uploadImage:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "94C8EF05-A97B-4855-ABDF-99F230BFF832"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/TakingPicturesWine(&#x62cd;&#x7167;&#x8bc6;&#x9152;)/LimCamera(&#x76f8;&#x673a;&#x8bc6;&#x522b;&#x9152;)/LimCamera/WineDetailsVC(&#x9152;&#x6b3e;&#x8be6;&#x60c5;)/&#x54c1;&#x9274;/WineDetailsTwoContentViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "31"
            endingLineNumber = "31"
            landmarkName = "-viewDidLoad"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2C74E0F6-98A8-4968-8EE2-B57F2F208A56"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/TakingPicturesWine(&#x62cd;&#x7167;&#x8bc6;&#x9152;)/LimCamera(&#x76f8;&#x673a;&#x8bc6;&#x522b;&#x9152;)/LimCamera/WineDetailsVC(&#x9152;&#x6b3e;&#x7b49;&#x8be6;&#x60c5;)/&#x4ea7;&#x533a;/RegionViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "216"
            endingLineNumber = "216"
            landmarkName = "-tableView:cellForRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8B3C619E-BBD6-4247-9FDC-65B62C00B2C6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Home(&#x9996;&#x9875;)/ViewController/Search/KnowledgeWineSerachResultsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "188"
            endingLineNumber = "188"
            landmarkName = "-tableView:didSelectRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2E7276F0-7A75-42DD-9009-E9FD42FEF361"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Home(&#x9996;&#x9875;)/ViewController/Search/KnowledgeWineSerachResultsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "133"
            endingLineNumber = "133"
            landmarkName = "-getUpdataList:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "319A85F5-1C65-406B-91C9-3C662501CDDE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/TakingPicturesWine(&#x62cd;&#x7167;&#x8bc6;&#x9152;)/LimCamera(&#x76f8;&#x673a;&#x8bc6;&#x522b;&#x9152;)/LimCamera/&#x62cd;&#x7167;&#x548c;&#x62cd;&#x7167;&#x8bc6;&#x522b;&#x5217;&#x8868;/DBUploadImgViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "197"
            endingLineNumber = "197"
            landmarkName = "-uploadPic:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F05C9256-F6AE-4D3F-AF9E-2BE56D3C9729"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteRabbitHeadOrderController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "43"
            endingLineNumber = "43"
            landmarkName = "-viewDidLoad"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B6D7808D-4B61-4BF1-AABD-64432F7D0678"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/ViewController/ShoppingCart(&#x8d2d;&#x7269;&#x8f66;)/ShoppingCartViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "329"
            endingLineNumber = "329"
            landmarkName = "ShoppingCartViewController"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4FF9C9D5-60A9-4383-A5EE-D10130467A8F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/View/&#x5546;&#x54c1;/GoodsShowView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "75"
            endingLineNumber = "75"
            landmarkName = "-setModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "48E80EE3-B7F0-499D-B621-99A82A475CEE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/View/&#x5546;&#x54c1;/GoodsShowView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "132"
            endingLineNumber = "132"
            landmarkName = "-setModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C695AA11-513C-48CA-B2E0-926A0CA5DD3C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x56fe;&#x7247;&#x4e0a;&#x4f20;/AliImageReshapeController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "148"
            endingLineNumber = "148"
            landmarkName = "-imageCropFrame"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "739A029C-DE0C-4139-9568-8D0D68733D2B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x56fe;&#x7247;&#x4e0a;&#x4f20;/AliImageReshapeController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "135"
            endingLineNumber = "135"
            landmarkName = "-imageCropFrame"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AB98B45C-0CD4-4656-95AB-D44296628FB6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x56fe;&#x7247;&#x4e0a;&#x4f20;/AliImageReshapeController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "116"
            endingLineNumber = "116"
            landmarkName = "-layoutScrollView"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "55FC500D-9098-4D58-B0F7-E5647DD913E3"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x56fe;&#x7247;&#x4e0a;&#x4f20;/AliImageReshapeController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "97"
            endingLineNumber = "97"
            landmarkName = "-layoutScrollView"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3A74841C-70E7-4620-B9B5-1399C7533FDF"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyCollect(&#x6211;&#x7684;&#x6536;&#x85cf;)/WineEvaluationCollectController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "269"
            endingLineNumber = "269"
            landmarkName = "-delete"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4940599D-1E05-4537-9595-943296AD005B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyCollect(&#x6211;&#x7684;&#x6536;&#x85cf;)/WineEvaluationCollectController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "267"
            endingLineNumber = "267"
            landmarkName = "-delete"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E22F43F1-34FA-4909-883A-663F190D1CB0"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyReception(&#x6211;&#x7684;&#x9152;&#x4f1a;||&#x9152;&#x4f1a;)/View/PartyPopView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "125"
            endingLineNumber = "125"
            landmarkName = "-initWithFrame:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "50BCC0A5-ADE0-4188-92D0-DD47029529C8"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/TakingPicturesWine(&#x62cd;&#x7167;&#x8bc6;&#x9152;)/LimCamera(&#x76f8;&#x673a;&#x8bc6;&#x522b;&#x9152;)/LimCamera/WineDetailsVC(&#x9152;&#x6b3e;&#x7b49;&#x8be6;&#x60c5;)/&#x9152;&#x6b3e;/WineDetaisVC.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "293"
            endingLineNumber = "293"
            landmarkName = "-tableView:didSelectRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A4ED3855-90AC-4BFE-A679-FA0F0001F182"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/View/&#x793e;&#x533a;/&#x793e;&#x533a;/PlateWineEvaluationView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "168"
            endingLineNumber = "168"
            landmarkName = "-updateView"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "54B26CFC-B4DC-4892-BD67-A82D4619A809"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/&#x793e;&#x533a;&#x9996;&#x9875;&#x8bdd;&#x9898;&#x8be6;&#x60c5;/TopicDetailViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "229"
            endingLineNumber = "229"
            landmarkName = "-requestData"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "42530376-5B5D-4577-9072-72AC6E526731"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/PostsAndVideo(&#x6211;&#x7684;--)/View/PostsTableViewCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "634"
            endingLineNumber = "634"
            landmarkName = "-setPost:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "162063CD-B81A-47B4-9C24-349B91F96C84"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x8ba2;&#x5355;&#x8be6;&#x60c5;/ReceptionOrderDetailsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "90"
            endingLineNumber = "90"
            landmarkName = "-viewDidLoad"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "928DAF58-D730-4A05-B5E9-1911CEEB7152"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/View/&#x76f4;&#x64ad;/LiveGoodsListView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "132"
            endingLineNumber = "132"
            landmarkName = "-tableView:cellForRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F1944B2A-2633-4031-A15C-86E1D4C9BA52"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/View/&#x76f4;&#x64ad;/LiveGoodsListView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "19"
            endingLineNumber = "19"
            landmarkName = "-initWithFrame:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "062D18E3-ECE8-450B-9E26-3BD9BD76C907"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/MeleeDetailsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "719"
            endingLineNumber = "719"
            landmarkName = "-tableView:cellForRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B675780D-5730-43BD-99DD-E063305B38E8"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/MeleeDetailsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "192"
            endingLineNumber = "192"
            landmarkName = "MeleeDetailsViewController"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "CF0346A7-6473-4A82-AA8A-15C553246268"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/MeleeDetailsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "246"
            endingLineNumber = "246"
            landmarkName = "-footRequestData"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4EE953A7-ABAA-459D-9AFF-C39E729C8E15"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/MeleeDetailsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "365"
            endingLineNumber = "365"
            landmarkName = "-MeleeFocusNotification:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2D680E0D-25D9-459F-82B1-A12200830E95"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/MeleeDetailsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "128"
            endingLineNumber = "128"
            landmarkName = "-yd_setupUI"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "244E7B8B-0F7F-464E-A330-664C9CB209F5"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/MeleeDetailsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "300"
            endingLineNumber = "300"
            landmarkName = "-antiSideComments:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "1F34740C-BE6B-41BD-90C0-5A2A20AC00C7"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/View/&#x79d2;&#x53d1;&#x4e00;&#x7ea7;&#x5934;&#x90e8;/MiaofaCardTwoView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "144"
            endingLineNumber = "144"
            landmarkName = "MiaofaCardTwoView"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "ACEF329C-FA0E-41B5-90E0-D528DCFE0C0A"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/View/&#x79d2;&#x53d1;&#x4e00;&#x7ea7;&#x5934;&#x90e8;/MiaofaCardTwoView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "139"
            endingLineNumber = "139"
            landmarkName = "-cycleScrollView:didSelectCellAtIndex:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2FE76387-E51D-4665-B0DD-6632FEB83E19"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/View/&#x79d2;&#x53d1;&#x4e00;&#x7ea7;&#x5934;&#x90e8;/MiaofaHeaderView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "267"
            endingLineNumber = "267"
            landmarkName = "MiaofaHeaderView"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AFBA7A10-947C-4DCC-82E1-295E0FEFCDDA"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/Login(&#x767b;&#x5f55;)/LoginTypeViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "54"
            endingLineNumber = "54"
            landmarkName = "-viewDidLoad"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.ExceptionBreakpoint">
         <BreakpointContent
            uuid = "E63F32C9-3A3C-473B-A5FC-3051530F6C02"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            breakpointStackSelectionBehavior = "1"
            scope = "1"
            stopOnStyle = "0">
            <Actions>
               <BreakpointActionProxy
                  ActionExtensionID = "Xcode.BreakpointAction.DebuggerCommand">
                  <ActionContent
                     consoleCommand = "">
                  </ActionContent>
               </BreakpointActionProxy>
            </Actions>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C0B30C27-0C96-4139-90B4-D456E829BB08"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/PushPostController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1423"
            endingLineNumber = "1423"
            landmarkName = "-pushWeiboContentAction:andType:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6BBB32F0-80A2-4C67-931B-E6611AA6985B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/PostsAndVideo(&#x6211;&#x7684;--)/Controller/MyCustomerVC.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "16"
            endingLineNumber = "16"
            landmarkName = "-dealloc"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "FBB0A51B-575F-4054-8DAA-F37F05250118"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/PostsAndVideo(&#x6211;&#x7684;--)/Controller/PostsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "497"
            endingLineNumber = "497"
            landmarkName = "-likeBtn:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5163FD65-4A55-48DE-BBDD-143D59F51798"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x5206;&#x4eab;/ShareShowObject.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "47"
            endingLineNumber = "47"
            landmarkName = "-displayShareView"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D2AF0833-7FD3-4456-9246-628621C18450"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/View/&#x79d2;&#x53d1;&#x4e00;&#x7ea7;&#x5934;&#x90e8;/MiaofaHeaderView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "362"
            endingLineNumber = "362"
            landmarkName = "MiaofaHeaderView"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6211AF75-81BF-49C9-95EA-403A930D531A"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/PushPostController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "773"
            endingLineNumber = "773"
            landmarkName = "-imagePickerController:didFinishPickingVideo:sourceAssets:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "059960CF-1AF7-440D-816E-4BE486867156"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/PushPostController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "687"
            endingLineNumber = "687"
            landmarkName = "-imagePickerController:didFinishPickingMediaWithInfo:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0A544EFD-48A4-46B6-8574-016E4BEE9B66"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/PersonView/PersonView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "197"
            endingLineNumber = "197"
            landmarkName = "-createTutouView"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "1251D559-1254-4971-A15D-B26C725A1526"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/PersonView/PersonView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "191"
            endingLineNumber = "191"
            landmarkName = "-createTutouView"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C1172B7C-E9FE-41F9-870C-12D47AFFEF2D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/ShareCertificateViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "511"
            endingLineNumber = "511"
            landmarkName = "-moreClick"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3B20520C-6B3A-4C25-AE79-F07C2C133C5F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x8ba2;&#x5355;&#x8be6;&#x60c5;/ReceptionOrderDetailsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "259"
            endingLineNumber = "259"
            landmarkName = "-getPeriodsRecommendList:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.ExceptionBreakpoint">
         <BreakpointContent
            uuid = "BB82CDCC-B534-48F9-8748-ADE91EFB9A0C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            breakpointStackSelectionBehavior = "1"
            scope = "1"
            stopOnStyle = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E6F103F0-F45B-4C48-AA26-F87B4BDC83D4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/MiaofaViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "794"
            endingLineNumber = "794"
            landmarkName = "-pushAMapLoactionViewController"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "935C6CBB-BEE8-46AF-B228-45DAB4102CD6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/MiaofaViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "997"
            endingLineNumber = "997"
            landmarkName = "MiaofaViewController"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3DD349C3-8005-4789-949E-27467DCF113C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/MiaofaViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "834"
            endingLineNumber = "834"
            landmarkName = "MiaofaViewController"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3CA95C8D-680E-452A-BD48-84C4F8E28EE7"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/WaterfallChooseWineCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "17"
            endingLineNumber = "17"
            landmarkName = "-initWithFrame:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "42A787DF-B65E-499E-A704-6EB9B24DACF6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/MiaofaViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "837"
            endingLineNumber = "837"
            landmarkName = "MiaofaViewController"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "761E1F15-99D6-4302-8A61-694FC2BD3F26"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/MiaofaViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "937"
            endingLineNumber = "937"
            landmarkName = "MiaofaViewController"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B442BEC4-D130-4E93-B071-E91B95F1C427"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/View/&#x793e;&#x533a;/&#x56e2;&#x6218;/&#x56e2;&#x6218;&#x8be6;&#x60c5;/MeleeDetailsCommentCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "144"
            endingLineNumber = "144"
            landmarkName = "-setLeftModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BE18B095-4059-484B-919C-6604575E2FE4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/View/&#x793e;&#x533a;/&#x56e2;&#x6218;/&#x56e2;&#x6218;&#x8be6;&#x60c5;/MeleeDetailsCommentCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "199"
            endingLineNumber = "199"
            landmarkName = "-setRightModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C2FBD0AA-C65F-47FC-9329-5884D0B157B1"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/View/&#x89c6;&#x9891;/GKDYCommentView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "527"
            endingLineNumber = "527"
            landmarkName = "-tableView:cellForRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C8195325-C5B7-41A1-AD93-EC5DACBD7289"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/View/&#x89c6;&#x9891;/GKDYCommentView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "532"
            endingLineNumber = "532"
            landmarkName = "-tableView:cellForRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B291D208-EC91-43ED-9990-32948F9AB10F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x89c6;&#x9891;/Alivc/AlivcSmartVideo/Classes/control/AlivcShortVideoLivePlayViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "463"
            endingLineNumber = "463"
            landmarkName = "-fetchedMoreVideos:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AC2AB56B-7133-4AC2-8956-6687EEEB04AF"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x89c6;&#x9891;/Alivc/AlivcSmartVideo/Classes/control/AlivcShortVideoLivePlayViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "339"
            endingLineNumber = "339"
            landmarkName = "-oneselfrequestData"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C192A3FF-284D-4482-8EA3-0A154C972E14"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x89c6;&#x9891;/Alivc/AlivcSmartVideo/Classes/model/AlivcShortVideoPlayerManager.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "146"
            endingLineNumber = "146"
            landmarkName = "-onError:errorModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "59CB7FBA-4D52-4B22-82B5-00990E2DEB01"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x89c6;&#x9891;/Alivc/AlivcSmartVideo/Classes/model/AlivcShortVideoPlayerManager.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "148"
            endingLineNumber = "148"
            landmarkName = "-onError:errorModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7F631293-B88A-4EFC-B565-9F8EB924D17D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/OrderListViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "605"
            endingLineNumber = "605"
            landmarkName = "-clickBtn:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A17DF220-5799-4549-B5E9-CAE8F45D2E83"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x5207;&#x6362;/GLEnvs.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "179"
            endingLineNumber = "179"
            landmarkName = "-applicationDidBecomeActive"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "87EEC402-17ED-4E64-9609-591DE9325EA4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x5207;&#x6362;/GLEnvs.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "186"
            endingLineNumber = "186"
            landmarkName = "-showEnvWindow"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DBBE2472-3338-4B9B-AB51-A1292BE41218"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x5207;&#x6362;/GLEnvs.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "204"
            endingLineNumber = "204"
            landmarkName = "-showEnvChanger"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4551241E-5364-49FD-B3C6-EDB40EDD42D0"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x5207;&#x6362;/GLEnvs.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "318"
            endingLineNumber = "318"
            landmarkName = "-enableWithShortCutItemChooseHandle:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.ExceptionBreakpoint">
         <BreakpointContent
            uuid = "22225374-EEAA-48A0-BD33-D215C49340E5"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            breakpointStackSelectionBehavior = "1"
            scope = "1"
            stopOnStyle = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4F4D7E77-FDCE-4BF8-B880-61751DBF79ED"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/View/&#x65b0;&#x7248;&#x79d2;&#x53d1;/DefaultHomePagePopView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "44"
            endingLineNumber = "44"
            landmarkName = "-homeBtnClick:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "ED466DF8-85B8-40BA-A390-4D0C906E3274"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/FlashDeliveryListController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "930"
            endingLineNumber = "930"
            landmarkName = "unknown"
            landmarkType = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AAE33901-8AD9-48FE-8DCF-F5F5038F3132"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/View/&#x793e;&#x533a;/&#x793e;&#x533a;/SubCommentView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "119"
            endingLineNumber = "119"
            landmarkName = "-setupWithLikeItemsArray:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "78320E11-FD1F-46C0-B05F-CC9DEB2E315A"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/View/&#x793e;&#x533a;/&#x793e;&#x533a;/SubCommentView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "133"
            endingLineNumber = "133"
            landmarkName = "-setupWithLikeItemsArray:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C26E2DCB-097B-4F86-BDDA-28A0A62DD0D6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/View/&#x65b0;&#x7248;&#x79d2;&#x53d1;/collectionViewCell/TopicRecommendationCollectionCell.m"
            startingColumnNumber = "24"
            endingColumnNumber = "47"
            startingLineNumber = "82"
            endingLineNumber = "82"
            landmarkName = "-setFrameModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "1D71D16F-55B4-4BAA-A38C-C545723A2D44"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/FlashDeliveryListController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1108"
            endingLineNumber = "1108"
            landmarkName = "unknown"
            landmarkType = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D44827C3-53F4-4C4F-B6BF-909A8F18A9F6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/MiaofaMore/MiaofaMoreViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "502"
            endingLineNumber = "502"
            landmarkName = "-getRightDataList:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "47DA8A31-1230-4B5A-90F3-597299F2E46B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/View/MiaofaListView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "203"
            endingLineNumber = "203"
            landmarkName = "-tableView:cellForRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B55A1EF2-489E-48D2-91E3-1D9C3CECEC6D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x5206;&#x4eab;/ShareShowObject.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "544"
            endingLineNumber = "544"
            landmarkName = "-touchItemView:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "31A12F19-A7FC-490C-BBE5-CCD45CE07D02"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x5206;&#x4eab;/ShareShowObject.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "143"
            endingLineNumber = "143"
            landmarkName = "-touchItemView:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3CBF0484-DE23-4150-8A4E-513304150DA7"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/FlashDeliveryListController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "953"
            endingLineNumber = "953"
            landmarkName = "unknown"
            landmarkType = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "62739816-33BA-4FD9-991B-DD1F32C0E923"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1388"
            endingLineNumber = "1388"
            landmarkName = "-getOrderTextJsonData:style:andCallBack:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "1D835CA9-72BE-4736-A399-492EEDB4F4AD"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/Address(&#x5730;&#x5740;)/AddressViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "795"
            endingLineNumber = "795"
            landmarkName = "-textViewDidChange:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6C4C0F52-B63A-42FF-96B6-3835FDAA3AF6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Home(&#x9996;&#x9875;)/ViewController/HomePageViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "279"
            endingLineNumber = "279"
            landmarkName = "-getNoticeNumData:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "157CCA8A-F9C7-4BAE-9034-BBF02035FB52"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/PostsAndVideo(&#x6211;&#x7684;--)/View/PostsTableViewCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "359"
            endingLineNumber = "359"
            landmarkName = "-setPost:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B33A3C41-9FEE-42D6-9358-7A78A5F7B676"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/CTAssetsPickerController/CTAssetsPickerController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "935"
            endingLineNumber = "935"
            landmarkName = "-imagePreviewView:renderZoomImageView:atIndex:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3C74FFF8-001D-43B0-B575-D61A8A0F550B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x56fe;&#x7247;&#x4e0a;&#x4f20;/AliUpLoadImageTool.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "178"
            endingLineNumber = "178"
            landmarkName = "-uploadOneVideo:andVideoData:isAddWaterMark:oSSClient:currentIndex:success:faile:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5C5B1545-D336-4E25-945B-6BAC959F5BFE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/FlashDeliveryListController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1091"
            endingLineNumber = "1091"
            landmarkName = "unknown"
            landmarkType = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "54891748-F192-4223-879B-266CBC5D4D2C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/View/InvoiceInfoView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "111"
            endingLineNumber = "111"
            landmarkName = "-setListArray:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DA6DA69C-48B7-4AAE-B5A7-2EC1C8C39F69"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/View/&#x62cd;&#x5356;/AuctionChooseImageView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "82"
            endingLineNumber = "82"
            landmarkName = "-initWithFrame:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D992FDB2-70E4-4BCA-A023-384A8E7D2FA6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/View/&#x62cd;&#x5356;/ReleaseLotPriceTableViewCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "629"
            endingLineNumber = "629"
            landmarkName = "-setConfigModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C07823E8-A86B-44D3-857E-68A07ACCC607"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/OrderListViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "789"
            endingLineNumber = "789"
            landmarkName = "-clickBtn:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "445DD01B-C370-4E5D-A8AC-D73A8E208512"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x62cd;&#x5356;/&#x53d1;&#x5e03;&#x62cd;&#x5356;/AuctionAtcAccountViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "262"
            endingLineNumber = "262"
            landmarkName = "-submitClickWithCode:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8BA0785D-8CAD-4A5A-8A02-F915DDDBEE4C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x62cd;&#x5356;/&#x53d1;&#x5e03;&#x62cd;&#x5356;/ReleaseLotPriceViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "309"
            endingLineNumber = "309"
            landmarkName = "-checkSubmit"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "FE4425E8-B732-4E60-9C06-130DDABFCEB8"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x62cd;&#x5356;/&#x53d1;&#x5e03;&#x62cd;&#x5356;/ReleaseLotBasicInfoViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "783"
            endingLineNumber = "783"
            landmarkName = "-checkAndSave"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BDFF43E5-6EBB-4FF3-BC36-6F44C606A752"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x62cd;&#x5356;/&#x53d1;&#x5e03;&#x62cd;&#x5356;/MyPostedSearchViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "494"
            endingLineNumber = "494"
            landmarkName = "-giveUpPostRuequest"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0BBD4A19-3C2F-4463-BEC3-65EB622C69E9"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x62cd;&#x5356;/&#x53d1;&#x5e03;&#x62cd;&#x5356;/ReleaseLotPriceViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "224"
            endingLineNumber = "224"
            landmarkName = "-bottomViewSureBtnClick"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BC12B350-F4E7-415C-ADA9-4EC49D152644"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x62cd;&#x5356;/&#x53d1;&#x5e03;&#x62cd;&#x5356;/ReleaseLotBasicInfoViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "616"
            endingLineNumber = "616"
            landmarkName = "-imagePickerController:didFinishPickingMediaWithInfo:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E24257CA-8E64-45CA-8F52-CF10493D4B75"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x62cd;&#x5356;/&#x53d1;&#x5e03;&#x62cd;&#x5356;/ReleaseLotOtherViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "471"
            endingLineNumber = "471"
            landmarkName = "-imagePickerController:didFinishPickingMediaWithInfo:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "FAE482DE-6717-40EC-B2C3-A8E17AFE119D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x56fe;&#x7247;&#x4e0a;&#x4f20;/Luban_iOS_Extension_h/UIImage+Luban_iOS_Extension_h.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "27"
            endingLineNumber = "27"
            landmarkName = "+lubanCompressImage:withMask:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C1EC2D2D-03B3-4607-9548-3218BD2F2356"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x56fe;&#x7247;&#x4e0a;&#x4f20;/Luban_iOS_Extension_h/UIImage+Luban_iOS_Extension_h.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "82"
            endingLineNumber = "82"
            landmarkName = "+lubanCompressImage:withMask:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "684871F1-9EF7-4163-AD6B-ED23C9F41A6D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x56fe;&#x7247;&#x4e0a;&#x4f20;/Luban_iOS_Extension_h/UIImage+Luban_iOS_Extension_h.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "114"
            endingLineNumber = "114"
            landmarkName = "+compressWithImage:thumbW:thumbH:size:withMask:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "743618A7-56A9-4607-B5A5-A2719A352727"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/InvoiceManagement(&#x53d1;&#x7968;&#x7ba1;&#x7406;)/Controller/InvoiceManagementViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "676"
            endingLineNumber = "676"
            landmarkName = "-modifInvoice:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "317EF1D0-32C4-4547-9502-07FAA4BCBC06"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/InvoiceManagement(&#x53d1;&#x7968;&#x7ba1;&#x7406;)/Controller/InvoiceManagementViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "680"
            endingLineNumber = "680"
            landmarkName = "-modifInvoice:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "35DA1C7F-AA1F-4D6D-920C-1BE0FAC7C139"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/View/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderInvoiceInfoCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "245"
            endingLineNumber = "245"
            landmarkName = "-showInvoiceinfoView"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "EFA6E020-4405-4062-80E9-6C013889CF1C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/View/&#x586b;&#x5199;&#x8ba2;&#x5355;/ConvenientlyBuyCollectionViewCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "138"
            endingLineNumber = "138"
            landmarkName = "-setPurchaseModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "42A59C43-D93C-4A6B-A3B0-A46FF1821EBB"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/InputView/InputView/WZMInputView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "83"
            endingLineNumber = "83"
            landmarkName = "-emojisKeyboardDidSelectSend:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8AB0AE50-54CA-4608-8C78-5461F2587208"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/InputView/InputView/WZMInputView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "93"
            endingLineNumber = "93"
            landmarkName = "-emojisKeyboard:didSelectText:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "09DAE96C-C5A7-4622-A283-56910B29F16F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/InputView/InputView/WZMInputView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "87"
            endingLineNumber = "87"
            landmarkName = "-emojisKeyboardDidSelectDelete:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6DB4C91F-B409-4A30-9D43-4544BEDB939C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/InputView/InputView/WZMInputView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "99"
            endingLineNumber = "99"
            landmarkName = "-moreKeyboard:didSelectType:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "358E2DD8-72FF-4817-9B1D-45FCD004BDF4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/InputView/InputView/WZMInputView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "75"
            endingLineNumber = "75"
            landmarkName = "-toolView:didChangeRecordType:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "594287D1-94BA-43AF-8119-3584C2EF46EF"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/InputView/InputView/WZMInputView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "100"
            endingLineNumber = "100"
            landmarkName = "-moreKeyboard:didSelectType:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3A03FD65-298A-448C-B640-4CAE9D57510D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/InputView/InputView/View/ToolView/WZMToolView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "154"
            endingLineNumber = "154"
            landmarkName = "-resetStatus"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B0C58EC7-A950-462E-B78C-EA627E783C73"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/InputView/BaseInputView/WZMBaseInputView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "324"
            endingLineNumber = "324"
            landmarkName = "-deleteSelectedText"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "49B14448-848B-4ED9-99A5-849B4BBDA86D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/InputView/BaseInputView/WZMBaseInputView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "315"
            endingLineNumber = "315"
            landmarkName = "-replaceSelectedTextWithText:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2F8A8A55-FA07-49E4-A7B8-657AEC5C0626"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/InputView/BaseInputView/WZMBaseInputView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "433"
            endingLineNumber = "433"
            landmarkName = "-textField:shouldChangeCharactersInRange:replacementString:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "965BC816-11A0-40E2-9E71-07504B559CE5"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/CommonFunc.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "197"
            endingLineNumber = "197"
            landmarkName = "-pushVC:andParams:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F6173F7B-B310-482F-B097-A8F5A196AE2C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/MsgCell/ChatBtnTagView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "285"
            endingLineNumber = "285"
            landmarkName = "-selectItem:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2D87CFA5-EED6-43B8-AEAE-0533600FCB3A"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/MsgCell/ChatBtnTagView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "288"
            endingLineNumber = "288"
            landmarkName = "-selectItem:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "93A0560C-94CA-4DAC-9174-BD995A3AEF12"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/MsgCell/ChatBtnTagView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "383"
            endingLineNumber = "383"
            landmarkName = "-updateTagViewLayout"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AABC1293-2C5B-44F9-BA55-829F890DAC7D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/View/MsgCell/ChatBtnTagView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "201"
            endingLineNumber = "201"
            landmarkName = "-addTagWithModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C3F97179-C01D-4567-8736-09E077F962BD"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyReception(&#x6211;&#x7684;&#x9152;&#x4f1a;||&#x9152;&#x4f1a;)/Controller/WinePartyDetailController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "71"
            endingLineNumber = "71"
            landmarkName = "-viewDidLoad"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2A2E1AF5-F2B1-433B-B728-9249FE03B9B7"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyReception(&#x6211;&#x7684;&#x9152;&#x4f1a;||&#x9152;&#x4f1a;)/Controller/WinePartyDetailController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "74"
            endingLineNumber = "74"
            landmarkName = "-viewDidLoad"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C618BB6C-0A16-46C4-A535-D187F657F095"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/MapNavi/MapNaviTool.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "44"
            endingLineNumber = "44"
            landmarkName = "-startMapNavi:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "179300CF-D4BF-49C4-8ADB-9A91AC79F2C4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/PostsAndVideo(&#x6211;&#x7684;--)/Controller/PostsAndVideoViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "165"
            endingLineNumber = "165"
            landmarkName = "-yd_setupUI"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "32255C0D-576A-45AB-B97E-8D0E9228DB30"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/CustomerService(&#x667a;&#x80fd;&#x5ba2;&#x670d;)/Chat/Controller/CustomerServiceChatViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "659"
            endingLineNumber = "659"
            landmarkName = "-btnClickWithData:cell:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "973040D3-8FC0-4851-AD19-DB6CD636EBB5"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/CommonFunc.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "349"
            endingLineNumber = "349"
            landmarkName = "-pushVC:andParams:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "EA6C1602-DF5E-44A5-8083-73C5B27F4854"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x4fa7;&#x6ed1;&#x7b5b;&#x9009;/GoodsFilterView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "397"
            endingLineNumber = "397"
            landmarkName = "-setupLayout"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "02C6AAD0-F083-4646-A412-33C5ED6AE9E8"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteReceptionViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "424"
            endingLineNumber = "424"
            landmarkName = "-tableView:didSelectRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "85DAFE27-3077-4FF4-8AFD-3616117EAD5A"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/TakingPicturesWine(&#x62cd;&#x7167;&#x8bc6;&#x9152;)/LimCamera(&#x76f8;&#x673a;&#x8bc6;&#x522b;&#x9152;)/LimCamera/WineDetailsVC(&#x9152;&#x6b3e;&#x7b49;&#x8be6;&#x60c5;)/&#x54c1;&#x9274;/ProductTasteTableViewCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "142"
            endingLineNumber = "142"
            landmarkName = "-setModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "EAB66518-6E7D-4C94-B194-D5C6E7B4853B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/TakingPicturesWine(&#x62cd;&#x7167;&#x8bc6;&#x9152;)/LimCamera(&#x76f8;&#x673a;&#x8bc6;&#x522b;&#x9152;)/LimCamera/WineDetailsVC(&#x9152;&#x6b3e;&#x7b49;&#x8be6;&#x60c5;)/&#x54c1;&#x9274;/ProductTasteTableViewCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "30"
            endingLineNumber = "30"
            landmarkName = "-awakeFromNib"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5D1D021F-E966-4BCD-A87A-C68DF55A20DD"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MessageCenter(&#x901a;&#x77e5;&#x4e2d;&#x5fc3;)/View/systemMessageInformsCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "338"
            endingLineNumber = "338"
            landmarkName = "-setModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4A0EEBD1-61A0-4D5C-BCDC-8154492992E9"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x8ba2;&#x5355;&#x8be6;&#x60c5;/OrderDetailsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1747"
            endingLineNumber = "1747"
            landmarkName = "-jumpKefu"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2295C3FA-68F4-4704-B7E9-8FDFFD4EE844"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x8d2d;&#x7269;&#x8f66;/GoodsInfoCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "224"
            endingLineNumber = "224"
            landmarkName = "-initializeView"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "653983E6-**************-3DFC3F4855A1"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/ViewController/ShoppingCart(&#x8d2d;&#x7269;&#x8f66;)/ShoppingCartViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1149"
            endingLineNumber = "1149"
            landmarkName = "-tableView:viewForHeaderInSection:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "ED18601A-8ADF-40D2-B663-E5B18392FDF4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "2268"
            endingLineNumber = "2268"
            landmarkName = "-tableView:cellForRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5F9119E1-E6C3-4407-9FBD-528F56D5DCB1"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/TakingPicturesWine(&#x62cd;&#x7167;&#x8bc6;&#x9152;)/LimCamera(&#x76f8;&#x673a;&#x8bc6;&#x522b;&#x9152;)/LimCamera/&#x62cd;&#x7167;&#x548c;&#x62cd;&#x7167;&#x8bc6;&#x522b;&#x5217;&#x8868;/DBUploadImgViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "299"
            endingLineNumber = "299"
            landmarkName = "-uploadImage:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A700A702-0C32-46E0-A3F0-71D9F8A1D834"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/TakingPicturesWine(&#x62cd;&#x7167;&#x8bc6;&#x9152;)/LimCamera(&#x76f8;&#x673a;&#x8bc6;&#x522b;&#x9152;)/LimCamera/&#x62cd;&#x7167;&#x548c;&#x62cd;&#x7167;&#x8bc6;&#x522b;&#x5217;&#x8868;/KnowledgeWineResultsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "372"
            endingLineNumber = "372"
            landmarkName = "-uploadImage:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2079CAAD-9FF1-43D7-93CF-84E2D60CF9E6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x4fa7;&#x6ed1;&#x7b5b;&#x9009;/GoodsFilterView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "263"
            endingLineNumber = "263"
            landmarkName = "-addmoneyModel"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E536D539-7810-487A-A9BD-2751E5C6D64E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x4fa7;&#x6ed1;&#x7b5b;&#x9009;/GoodsFilterView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "199"
            endingLineNumber = "199"
            landmarkName = "-dataUpdate:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6AB1B00A-B98D-41ED-AD7E-9B1937D109B8"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x4fa7;&#x6ed1;&#x7b5b;&#x9009;/GoodsFilterView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "439"
            endingLineNumber = "439"
            landmarkName = "-setupLayout"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4DF202F7-8BD5-4A72-A256-3C61224DBD2D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x4fa7;&#x6ed1;&#x7b5b;&#x9009;/GoodsFilterView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "435"
            endingLineNumber = "435"
            landmarkName = "-setupLayout"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DDE19EC4-4AFF-4E9A-85F9-2FD9B8870475"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x4fa7;&#x6ed1;&#x7b5b;&#x9009;/GoodsFilterView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "384"
            endingLineNumber = "384"
            landmarkName = "-setupLayout"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6F4D31D3-A1FF-4138-A2AB-9D599C8326C1"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x4fa7;&#x6ed1;&#x7b5b;&#x9009;/GoodsFilterView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "389"
            endingLineNumber = "389"
            landmarkName = "-setupLayout"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "1F8458E0-C68D-429A-9A46-67CE33807D92"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x4fa7;&#x6ed1;&#x7b5b;&#x9009;/GoodsFilterView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "370"
            endingLineNumber = "370"
            landmarkName = "-setupLayout"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "FA6725F2-11E0-47A8-92F6-B68EC0353D09"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x4fa7;&#x6ed1;&#x7b5b;&#x9009;/GoodsFilterView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "373"
            endingLineNumber = "373"
            landmarkName = "-setupLayout"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "066ED632-7F05-497C-9447-828930F9C750"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x4fa7;&#x6ed1;&#x7b5b;&#x9009;/GoodsFilterView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "687"
            endingLineNumber = "687"
            landmarkName = "-textFieldDidEndEditing:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0DE6E75B-15E0-48E1-BC98-A55466C1B0CB"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/&#x4fa7;&#x6ed1;&#x7b5b;&#x9009;/GoodsFilterView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "698"
            endingLineNumber = "698"
            landmarkName = "-textFieldDidEndEditing:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "705F0379-E5C6-4EB4-9C13-D7B67FC1E139"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/ViewController/FlashSalesCustViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "534"
            endingLineNumber = "534"
            landmarkName = "-requestData"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "37DC7EE6-A23E-4231-ABB4-F83DEF8D243E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/CommonFunc.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "581"
            endingLineNumber = "581"
            landmarkName = "-pushVC:andParams:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "44DABBB6-DB23-40A9-B48D-F7CA19C0EA93"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1233"
            endingLineNumber = "1233"
            landmarkName = "-getCouponList:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "580A0A3B-E8A5-42AB-86DD-98B72AE6BD92"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "529"
            endingLineNumber = "529"
            landmarkName = "-getShipmentypefreight:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "00C12E0B-A3A6-4C6C-9320-D6E1809E481A"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "619"
            endingLineNumber = "619"
            landmarkName = "-getShipmentypefreight:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8A1419E2-8BD9-48AC-82F0-FB8A42E6D09F"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "609"
            endingLineNumber = "609"
            landmarkName = "-getShipmentypefreight:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B8D90F94-A5D4-45F9-99CE-583ADD9C09A0"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/AppDelegate.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "530"
            endingLineNumber = "530"
            landmarkName = "-jumpToTaobaoLink:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "66B7C2AB-C518-44DD-B052-8233A4EA044E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Iflashbuy(&#x95ea;&#x8d2d;)/View/SpiritsGoodsCollectionViewCell.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "198"
            endingLineNumber = "198"
            landmarkName = "-setModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "195DF352-DA2D-4CAA-B11C-5AC30F512945"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/CommonFunc.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "577"
            endingLineNumber = "577"
            landmarkName = "-pushVC:andParams:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BAD0793A-3942-46CD-A865-48D9F06593A4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/MiaofaMore/MiaofaMoreLeftViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "92"
            endingLineNumber = "92"
            landmarkName = "-tableView:cellForRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "CB1CDF0B-3FEC-497E-AA35-509B60E64534"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/MiaofaMore/MiaofaMoreLeftViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "97"
            endingLineNumber = "97"
            landmarkName = "-tableView:cellForRowAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3E4969A2-8F0D-42AE-9EC4-6C4B8DB2E627"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Main(&#x914d;&#x7f6e;)/MyURLSchemeHandler.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "23"
            endingLineNumber = "23"
            landmarkName = "-webView:startURLSchemeTask:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E32B17E8-6535-4C17-A6C5-D9803740F4A4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/MiaofaViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "694"
            endingLineNumber = "694"
            landmarkName = "-getPreCommodityList"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "156E8EC1-CBF1-4235-8B75-13BB58C2308B"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x8ba2;&#x5355;&#x8be6;&#x60c5;/CheckLogisticsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "137"
            endingLineNumber = "137"
            landmarkName = "-getLogisticsInfo:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "499E09E4-3905-4CA7-A7CD-2ACECE3FF273"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/PushPostController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1277"
            endingLineNumber = "1277"
            landmarkName = "-pushWeiboAction"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6218BA4B-89F8-44F8-B880-709E02D8D341"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Miaofa(&#x79d2;&#x53d1;)/ViewController/MiaofaViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "744"
            endingLineNumber = "744"
            landmarkName = "-getPreCommodityList"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "945F3EDD-A80B-4D7C-B628-9E3C5B851B08"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x56fe;&#x7247;&#x4e0a;&#x4f20;/AliUpLoadImageTool.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "258"
            endingLineNumber = "258"
            landmarkName = "-upLoadAlilImage:andWathermark:andAdditionalWH:imageDataArray:success:faile:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2EC68D76-5847-41D6-9DAE-21DD542539EE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/&#x56fe;&#x7247;&#x4e0a;&#x4f20;/AliUpLoadImageTool.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "63"
            endingLineNumber = "63"
            landmarkName = "-upLoadAlilVideo:andWathermark:imageDataArray:success:faile:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AB1C309F-2B3F-406D-95FE-CB8073D538E2"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/View/&#x8ba2;&#x5355;&#x8be6;&#x60c5;/OrderDetailOne.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "556"
            endingLineNumber = "556"
            landmarkName = "-setModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E4DE3827-BB89-4852-9CC6-A4AC78419F05"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/OrderListViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "704"
            endingLineNumber = "704"
            landmarkName = "-clickBtn:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C7124A1A-CC1A-4A33-B6DA-CD230CAB306D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/PlateDetailViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "419"
            endingLineNumber = "419"
            landmarkName = "-getPostData:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6F5A8201-BC98-4C09-B852-F2E8B780E90D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Find(&#x53d1;&#x73b0;)/ViewController/&#x793e;&#x533a;/TopicListViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "230"
            endingLineNumber = "230"
            landmarkName = "-getTopList:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C881402B-8B5C-4DEF-AB88-87E7FA270A23"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "2437"
            endingLineNumber = "2437"
            landmarkName = "-showOneListView"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F079184F-3F8C-47C5-9C26-0040AFB55691"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "2495"
            endingLineNumber = "2495"
            landmarkName = "-showTwoListView:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B6F4CDAB-D8B9-4A41-87FD-895C7A53EC0D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "2539"
            endingLineNumber = "2539"
            landmarkName = "-showThreeListView:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "36802785-F40A-4A78-B733-B829D22A77D2"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "2771"
            endingLineNumber = "2771"
            landmarkName = "-chooseGoodsClickWithIndex:"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "36802785-F40A-4A78-B733-B829D22A77D2 - dec61d23f9c7cdde"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "-[WriteOrderViewController chooseGoodsClickWithIndex:]"
                  moduleName = "WineTalk"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/v3/WineTalk/Class/ViewController/My(%E6%88%91%E7%9A%84)/MyAllOrder(%E6%88%91%E7%9A%84%E8%AE%A2%E5%8D%95)/Controller/%E5%A1%AB%E5%86%99%E8%AE%A2%E5%8D%95/WriteOrderViewController.m"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "2771"
                  endingLineNumber = "2771">
               </Location>
               <Location
                  uuid = "36802785-F40A-4A78-B733-B829D22A77D2 - 8bfc7f0a8eff91f4"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "__54-[WriteOrderViewController chooseGoodsClickWithIndex:]_block_invoke"
                  moduleName = "WineTalk"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/v3/WineTalk/Class/ViewController/My(%E6%88%91%E7%9A%84)/MyAllOrder(%E6%88%91%E7%9A%84%E8%AE%A2%E5%8D%95)/Controller/%E5%A1%AB%E5%86%99%E8%AE%A2%E5%8D%95/WriteOrderViewController.m"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "2772"
                  endingLineNumber = "2772">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4D67E993-3829-475E-A785-18976E41E079"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1045"
            endingLineNumber = "1045"
            landmarkName = "-updatePayMoney:"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "4D67E993-3829-475E-A785-18976E41E079 - c58cfc569b65621e"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "__43-[WriteOrderViewController updatePayMoney:]_block_invoke.251"
                  moduleName = "WineTalk"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/v3/WineTalk/Class/ViewController/My(%E6%88%91%E7%9A%84)/MyAllOrder(%E6%88%91%E7%9A%84%E8%AE%A2%E5%8D%95)/Controller/%E5%A1%AB%E5%86%99%E8%AE%A2%E5%8D%95/WriteOrderViewController.m"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "1045"
                  endingLineNumber = "1045">
               </Location>
               <Location
                  uuid = "4D67E993-3829-475E-A785-18976E41E079 - 5544b19378ea6b4f"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "__43-[WriteOrderViewController updatePayMoney:]_block_invoke_2.252"
                  moduleName = "WineTalk"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/v3/WineTalk/Class/ViewController/My(%E6%88%91%E7%9A%84)/MyAllOrder(%E6%88%91%E7%9A%84%E8%AE%A2%E5%8D%95)/Controller/%E5%A1%AB%E5%86%99%E8%AE%A2%E5%8D%95/WriteOrderViewController.m"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "1046"
                  endingLineNumber = "1046">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A639BAF7-5E77-4E5F-A6F8-1F79B99384C3"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1476"
            endingLineNumber = "1476"
            landmarkName = "-getOrderTextJsonData:style:andCallBack:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B8FDC862-3294-4DE9-9D4A-54D6A8311897"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x586b;&#x5199;&#x8ba2;&#x5355;/WriteOrderViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1698"
            endingLineNumber = "1698"
            landmarkName = "-submitOrder:"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "B8FDC862-3294-4DE9-9D4A-54D6A8311897 - 2e6339ea22297be0"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "-[WriteOrderViewController submitOrder:]"
                  moduleName = "WineTalk"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/v3/WineTalk/Class/ViewController/My(%E6%88%91%E7%9A%84)/MyAllOrder(%E6%88%91%E7%9A%84%E8%AE%A2%E5%8D%95)/Controller/%E5%A1%AB%E5%86%99%E8%AE%A2%E5%8D%95/WriteOrderViewController.m"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "1698"
                  endingLineNumber = "1698">
               </Location>
               <Location
                  uuid = "B8FDC862-3294-4DE9-9D4A-54D6A8311897 - af2de967b0c044f9"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "__40-[WriteOrderViewController submitOrder:]_block_invoke"
                  moduleName = "WineTalk"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/v3/WineTalk/Class/ViewController/My(%E6%88%91%E7%9A%84)/MyAllOrder(%E6%88%91%E7%9A%84%E8%AE%A2%E5%8D%95)/Controller/%E5%A1%AB%E5%86%99%E8%AE%A2%E5%8D%95/WriteOrderViewController.m"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "1699"
                  endingLineNumber = "1699">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "305D961A-3C65-413B-A4FC-B611FDA16796"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x8ba2;&#x5355;&#x8be6;&#x60c5;/OrderDetailsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1561"
            endingLineNumber = "1561"
            landmarkName = "-changeTSTime"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DBE59695-D1C4-476D-AAD9-689F69022BBA"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/View/OrderShopView.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "249"
            endingLineNumber = "249"
            landmarkName = "-setModel:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "11781320-6763-4765-A197-4F909A37EA2D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/AxcAE_TabBar/AxcAE_TabBar.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "276"
            endingLineNumber = "276"
            landmarkName = "-itemDidLayoutBulge"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C22D4FE8-CDD4-4B52-BE3E-8C7CECF76DB5"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Home(&#x9996;&#x9875;)/ViewController/Search/SearchReturnViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "852"
            endingLineNumber = "852"
            landmarkName = "-collectionView:didSelectItemAtIndexPath:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "1E1D84D1-C422-48AE-8F1A-AD364739D06D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/CommonFunc.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "302"
            endingLineNumber = "302"
            landmarkName = "-pushVC:andParams:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7355A694-3360-40EB-A260-51CB3541B7D2"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Main(&#x914d;&#x7f6e;)/GeneralWebViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "262"
            endingLineNumber = "262"
            landmarkName = "-yd_setupUI"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "17865A9B-121B-4CD7-8443-2A343741D71A"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Main(&#x914d;&#x7f6e;)/GeneralWebViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "151"
            endingLineNumber = "151"
            landmarkName = "-loadURL:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E3CF5A9E-5F2E-4B52-BC90-F8D46890FE3F"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/Toos/CommonFunc.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "149"
            endingLineNumber = "149"
            landmarkName = "-pushVC:andParams:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "6B0C83E7-E3E0-4955-B42F-139EFE65DD59"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/Home(&#x9996;&#x9875;)/ViewController/HomePageViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "365"
            endingLineNumber = "365"
            landmarkName = "-webView:didFailProvisionalNavigation:withError:"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "6B0C83E7-E3E0-4955-B42F-139EFE65DD59 - a658d51ff364832a"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "-[HomePageViewController webView:didFailProvisionalNavigation:withError:]"
                  moduleName = "WineTalk"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/v3/WineTalk/Class/ViewController/Home(%E9%A6%96%E9%A1%B5)/ViewController/HomePageViewController.m"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "365"
                  endingLineNumber = "365">
               </Location>
               <Location
                  uuid = "6B0C83E7-E3E0-4955-B42F-139EFE65DD59 - 7e712c0f3321d3b9"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "__73-[HomePageViewController webView:didFailProvisionalNavigation:withError:]_block_invoke"
                  moduleName = "WineTalk"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/v3/WineTalk/Class/ViewController/Home(%E9%A6%96%E9%A1%B5)/ViewController/HomePageViewController.m"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "366"
                  endingLineNumber = "366">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DEC4369F-04CD-4C37-8F2A-9FBA34C91CBD"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/MyAllOrder(&#x6211;&#x7684;&#x8ba2;&#x5355;)/Controller/&#x8ba2;&#x5355;&#x8be6;&#x60c5;/OrderDetailsViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "369"
            endingLineNumber = "369"
            landmarkName = "-loadBottomFunction"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8B2CBAC1-2863-4304-AC32-203693D5259E"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "WineTalk/Class/ViewController/My(&#x6211;&#x7684;)/Login(&#x767b;&#x5f55;)/LoginViewController.m"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1042"
            endingLineNumber = "1042"
            landmarkName = "-showAgreementAlert"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "8B2CBAC1-2863-4304-AC32-203693D5259E - 88e7d4269fb9e2b6"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "__41-[LoginViewController showAgreementAlert]_block_invoke_2"
                  moduleName = "WineTalk"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/v3/WineTalk/Class/ViewController/My(%E6%88%91%E7%9A%84)/Login(%E7%99%BB%E5%BD%95)/LoginViewController.m"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "1042"
                  endingLineNumber = "1042">
               </Location>
               <Location
                  uuid = "8B2CBAC1-2863-4304-AC32-203693D5259E - d8e3a784a23af6f"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "__41-[LoginViewController showAgreementAlert]_block_invoke.323"
                  moduleName = "WineTalk"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/v3/WineTalk/Class/ViewController/My(%E6%88%91%E7%9A%84)/Login(%E7%99%BB%E5%BD%95)/LoginViewController.m"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "1042"
                  endingLineNumber = "1042">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
