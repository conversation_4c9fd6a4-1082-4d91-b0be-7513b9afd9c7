//
//  LoginViewController.m
//  WineTalk
//
//  Created by wineyun on 2021/6/21.
//

#import "LoginViewController.h"
#import "LoginTypeViewController.h"
#import "YDTabBarViewController.h"
#import "AppDelegate.h"
#import "CodeInputViewController.h"

#import "YYText.h"
#import "WXApi.h"

#import "WXLoginManager.h"
#import "WeakScriptMessageDelegate.h"
#import "configurationViewController.h"

#import <WebKit/WebKit.h>
#import <GTSDK/GeTuiSdk.h>
#import <ShareSDK/ShareSDK.h>
#import <QYSDK/QYSDK.h>
#import <CL_ShanYanSDK/CL_ShanYanSDK.h>
#import <IQKeyboardManager/IQKeyboardManager.h>

#import <AuthenticationServices/AuthenticationServices.h> //苹果登录
#import "BindMobilePhoneViewController.h"
#import "NSDate+ZJHelperKit.h"

@interface LoginViewController ()<UITextFieldDelegate,WKUIDelegate,WKNavigationDelegate,WKScriptMessageHandler, QMUITextFieldDelegate, ASAuthorizationControllerDelegate,ASAuthorizationControllerPresentationContextProviding>

@property (nonatomic, assign) BOOL weChatInstall; //是否安装微信

@property (nonatomic, strong) NSTimer            *codeTimer;
@property (nonatomic, assign) NSInteger           codeNumber;
@property (nonatomic, strong) WKWebView *webview;
@property (nonatomic, strong) WKUserContentController* userContentController;
@property (nonatomic, strong) UIImageView *logoimg;
@property (nonatomic, strong) QMUITextField *phoneTF;
@property (nonatomic, strong) QMUITextField *codeTF;
@property (nonatomic, strong) UIButton *getCodeBtn;
@property (nonatomic, strong) UIButton *loginButton;
@property (nonatomic, strong) YYLabel *agressLabel;
@property (nonatomic, strong) UIButton *wxLoginBtn;
@property (nonatomic, assign) BOOL isSelect;
@property (nonatomic, assign) NSInteger numberSelect; //点击次数跳转测试

//用于一键登录退出
@property (nonatomic, assign) BOOL isOnePush;
/// 进入页面时间
@property (nonatomic, strong) NSDate *intoDate;
@end

@implementation LoginViewController

- (void)dealloc{
//    setSourceEvent(@"");
//    setSourcePlatform(@"");
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
    [IQKeyboardManager sharedManager].enable = NO;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [IQKeyboardManager sharedManager].enable = YES;
    [self.codeTimer invalidate];
    self.codeTimer = nil;
    [self.userContentController removeScriptMessageHandlerForName:@"getTickit"];
    self.webview.UIDelegate = nil;
    self.webview.navigationDelegate = nil;
    self.webview = nil;
    if (@available(iOS 13.0, *)) {
        [[NSNotificationCenter defaultCenter] removeObserver:self name:ASAuthorizationAppleIDProviderCredentialRevokedNotification object:nil];
    }
//    self
    
}

- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
   
    
}

- (void)viewDidLoad{
    [super viewDidLoad];
    if(_source_event) {
        setSourceEvent(_source_event);
    }
    if(_source_platform) {
        setSourcePlatform(_source_platform);
    }
    _intoDate = [NSDate date];
    self.view.backgroundColor = UIColorWhite;
    [CLShanYanSDKManager preGetPhonenumber:nil];
    
    self.isOnePush = 1;
    self.weChatInstall=NO;
    if([WXApi isWXAppInstalled])
    {
        self.weChatInstall=YES;
    }
    
    self.codeNumber = 60;
    
    [self layOutSubViews];
    if ([HYTools changeUidIsNull]) {
        [HYTools GeTuiSdkUnbindAlias];
        [HYTools resetDefaults];
        [[UserInforObject userSingleton]removeUserDictionary];
        //七鱼客服注销
        [[QYSDK sharedSDK] logout:^(BOOL success) {}];
        //微信登出
        [ShareSDK cancelAuthorize:SSDKPlatformTypeWechat result:nil];
    }
    MJWeakSelf
    [self addRightNavButton:^{
       
    } title:@"其他方式登录"];
}

- (void)endViewEditing{
    [self.phoneTF resignFirstResponder];
    [self.codeTF resignFirstResponder];
    [self.webview removeFromSuperview];
}

- (void)layOutSubViews{
    MJWeakSelf
    
    UIImageView *bgImageView = [UIImageView new];
    bgImageView.userInteractionEnabled = YES;
    bgImageView.image = UIImageMake(@"login_back_img");
    [self.view addSubview:bgImageView];

    [bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.right.mas_equalTo(self.view);
    }];

//    [self.view addTapGesture:^(UIView *label) {
//        [weakSelf endViewEditing];
//    }];
    
    UIButton *backBtn = [[UIButton alloc] initWithFrame:CGRectMake(15, SafeTop+13, 24, 24)];
    [backBtn setImage:[UIImage imageNamed:@"avcBackIcon"] forState:UIControlStateNormal];
    [backBtn addUpInside:^(UIButton *button) {
        if([getEnterGuide integerValue]==1) {
            [weakSelf returnToRoot];
        } else {
            [weakSelf.navigationController popViewControllerAnimated:YES];
        }
        
    }];
    [self.view addSubview:backBtn];
    
    self.logoimg = [UIImageView new];
    [self.logoimg setImage:[UIImage imageNamed:@"welcomewineyune"]];
    self.logoimg.userInteractionEnabled = YES;
    [self.view addSubview:self.logoimg];
    
    self.numberSelect = 0;
    [self.logoimg addTapGesture:^(UIView *label) {
        weakSelf.numberSelect = weakSelf.numberSelect+1;
        if (weakSelf.numberSelect >= 30) {
            __block NSString *password;
            QMUIDialogTextFieldViewController *dialogViewController = [[QMUIDialogTextFieldViewController alloc] init];
            dialogViewController.title = @"---请输入---";
            [dialogViewController addTextFieldWithTitle:nil configurationHandler:^(QMUILabel *titleLabel, QMUITextField *textField, CALayer *separatorLayer) {
                textField.placeholder = @"点击键盘 Return 键视为点击确定按钮";
                textField.maximumTextLength = 30;
            }];
            dialogViewController.shouldManageTextFieldsReturnEventAutomatically = YES;// 让键盘的 Return 键也能触发确定按钮的事件。这个属性默认就是 YES，这里为写出来只是为了演示
            [dialogViewController addCancelButtonWithText:@"取消" block:nil];
            dialogViewController.shouldEnableSubmitButtonBlock = ^BOOL(QMUIDialogTextFieldViewController *aDialogViewController) {
                // 条件改为一定要写满5位才允许提交
                password = aDialogViewController.textFields.firstObject.text;
                return YES;
            };
            [dialogViewController addSubmitButtonWithText:@"确定" block:^(QMUIDialogViewController *dialogViewController) {
                if ([password isEqualToString:@"jiuyunwang1230"]) {
                    configurationViewController *vc = [[configurationViewController alloc]init];
                    [weakSelf.navigationController pushViewController:vc animated:YES];
                }
                [dialogViewController hide];
            }];
            [dialogViewController show];
            weakSelf.numberSelect = 0;
        }
    }];
    
    [self.logoimg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.view);
        make.width.offset(171);
        make.height.offset(136);
        make.top.equalTo(self.view.mas_top).offset(NavigationContentTop+20);
    }];
    
    DLog(@"%@--",[CLShanYanSDKManager clShanYanSDKVersion]);
    
    [self yijianLoginBtnAction];
}

- (void)bottomInit{
    MJWeakSelf
    weakSelf.phoneTF = [[QMUITextField alloc]init];
    weakSelf.phoneTF.font = [UIFont PingFangSCRegular:17];
    weakSelf.phoneTF.keyboardType = UIKeyboardTypeNumberPad;
    NSString *testText = @"请输入手机号";
    NSMutableAttributedString *placeholder = [[NSMutableAttributedString alloc] initWithString: testText];
    [placeholder addAttribute:NSFontAttributeName
                        value:[UIFont PingFangSCRegular:17]
                        range:NSMakeRange(0, testText.length)];
    
    weakSelf.phoneTF.attributedPlaceholder = placeholder;
    weakSelf.phoneTF.maximumTextLength = 11;
    weakSelf.phoneTF.textColor = UIColorWhite;
    weakSelf.phoneTF.placeholderColor = UIColorWhite;
    weakSelf.phoneTF.delegate = weakSelf;
    [weakSelf.phoneTF addTarget:weakSelf action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
    weakSelf.phoneTF.clearButtonMode=UITextFieldViewModeWhileEditing;
    [weakSelf.view addSubview:weakSelf.phoneTF];
    
    [weakSelf.phoneTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(weakSelf.view.mas_left).offset(33);
        make.right.equalTo(weakSelf.view.mas_right).offset(-33);
        make.height.offset(58);
        make.top.equalTo(weakSelf.logoimg.mas_bottom).offset(60);
    }];
    
    UIView *lineView = [[UIView alloc]init];
    lineView.backgroundColor = UIColorMakeWithHex(@"#EEEEEE");
    [weakSelf.view addSubview:lineView];
    
    [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(weakSelf.phoneTF);
        make.top.equalTo(weakSelf.phoneTF.mas_bottom);
        make.height.offset(1);
    }];
    
    weakSelf.codeTF = [[QMUITextField alloc]init];
    weakSelf.codeTF.font = [UIFont PingFangSCRegular:17];
    weakSelf.codeTF.keyboardType = UIKeyboardTypeNumberPad;
    weakSelf.codeTF.placeholder = @"请输入验证码";
    weakSelf.codeTF.delegate = weakSelf;
    weakSelf.codeTF.maximumTextLength = 10;
    weakSelf.codeTF.textColor = UIColorWhite;
    weakSelf.codeTF.placeholderColor = UIColorWhite;
    [weakSelf.codeTF addTarget:weakSelf action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
    [weakSelf.view addSubview:weakSelf.codeTF];
    
    [weakSelf.codeTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(lineView.mas_bottom).offset(13);
        make.left.equalTo(weakSelf.view.mas_left).offset(33);
        make.right.equalTo(weakSelf.view.mas_right).offset(-133);
        make.height.offset(58);
    }];
    
    weakSelf.getCodeBtn = [[UIButton alloc]init];
    [weakSelf.getCodeBtn.titleLabel setFont:[UIFont boldPingFangSCOfSize:15]];
    weakSelf.getCodeBtn.layer.masksToBounds = YES;
    weakSelf.getCodeBtn.layer.cornerRadius = 4;
    [weakSelf codeAvailable:NO];
    [weakSelf.getCodeBtn setTitle:NSLocalizedString(@"获取验证码", nil) forState:UIControlStateNormal];
    [weakSelf.getCodeBtn addTarget:weakSelf action:@selector(getPhoneCode:) forControlEvents:UIControlEventTouchUpInside];
    [weakSelf.view addSubview:weakSelf.getCodeBtn];
    
    [weakSelf.getCodeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.offset(31);
        make.centerY.mas_equalTo(weakSelf.codeTF);
        make.right.equalTo(weakSelf.view.mas_right).offset(-33);
        make.width.offset(95);
    }];
    
    UIView *codelineView = [[UIView alloc]init];
    codelineView.backgroundColor = UIColorMakeWithHex(@"#EEEEEE");
    [weakSelf.view addSubview:codelineView];
    [codelineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(weakSelf.codeTF);
        make.top.equalTo(weakSelf.codeTF.mas_bottom);
        make.height.offset(1);
    }];
    
    weakSelf.loginButton=[[UIButton alloc]init];
    [weakSelf.loginButton setTitle:@"登录" forState:UIControlStateNormal];
    // 设置按钮文字大小
    [weakSelf.loginButton.titleLabel setFont:[UIFont boldPingFangSCOfSize:18]];
    // 设置按钮文字颜色
    [weakSelf.loginButton setBackgroundColor:UIColorMakeWithHex(@"#E58E93")];
    [weakSelf.loginButton setTitleColor:UIColorWhite forState:UIControlStateNormal];
    // 按钮文字的位置
    // 添加点击事件
    [weakSelf.loginButton addTarget:weakSelf action:@selector(loginFunction) forControlEvents:UIControlEventTouchUpInside];
    weakSelf.loginButton.layer.cornerRadius = 26;
    [weakSelf.view addSubview:weakSelf.loginButton];
    
    [weakSelf.loginButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(weakSelf.view.mas_left).offset(26);
        make.right.equalTo(weakSelf.view.mas_right).offset(-26);
        make.top.equalTo(codelineView.mas_bottom).offset(35);
        make.height.offset(50);
    }];
    
    _agressLabel = [[YYLabel alloc]initWithFrame:CGRectMake(20, 100, weakSelf.view.bounds.size.width - 70, 32)];
    _agressLabel.textVerticalAlignment = YYTextVerticalAlignmentCenter;
    _agressLabel.textAlignment = NSTextAlignmentCenter;
    _agressLabel.userInteractionEnabled = YES;
    [weakSelf.view addSubview:_agressLabel];
    
    [_agressLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(weakSelf.view.mas_bottom).offset(-FIX_SCREEN_SCALE(31));
        make.left.equalTo(weakSelf.view.mas_left).offset(36);
        make.right.equalTo(weakSelf.view.mas_right).offset(-36);
        make.height.offset(30);
    }];
    
    UILabel *quickLabel = [UILabel new];
    quickLabel.backgroundColor = UIColorClear;
    quickLabel.text = @"快捷登录";
    quickLabel.textAlignment = NSTextAlignmentCenter;
    quickLabel.font = [UIFont PingFangSCRegular:14];
    quickLabel.textColor = UIColorWhite;
    [weakSelf.view addSubview:quickLabel];
    
    [quickLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(weakSelf.view);
        make.bottom.equalTo(weakSelf.agressLabel.mas_top).offset(-FIX_SCREEN_SCALE(89));
    }];
    
    UIImageView *lineLeftView = [[UIImageView alloc]init];
    lineLeftView.image = UIImageMake(@"login_left_line");
    [weakSelf.view addSubview:lineLeftView];
    
    [lineLeftView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.offset(1);
        make.right.equalTo(quickLabel.mas_left).offset(-10);
        make.centerY.equalTo(quickLabel);
    }];
    
    UIImageView *lineRightView = [[UIImageView alloc]init];
    lineRightView.image = UIImageMake(@"login_right_line");
    [weakSelf.view addSubview:lineRightView];
    
    [lineRightView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.offset(1);
        make.left.equalTo(quickLabel.mas_right).offset(10);
        make.centerY.equalTo(quickLabel);
    }];
    
    UIStackView *stackView = [UIStackView new];
    stackView.axis = UILayoutConstraintAxisHorizontal;
    stackView.alignment = UIStackViewAlignmentCenter;
    stackView.distribution = UIStackViewDistributionFillEqually;
    [weakSelf.view addSubview:stackView];
    
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(quickLabel.mas_bottom).offset(22);
        make.height.offset(52);
        make.right.equalTo(weakSelf.view.mas_right).offset(-52);
        make.left.equalTo(weakSelf.view.mas_left).offset(52);
    }];
    
    UIView *wxLoginView = [UIView new];
    [stackView addArrangedSubview:wxLoginView];
    
    [wxLoginView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@52);
//        make.width.equalTo(@52);
    }];
    
    _wxLoginBtn = [UIButton new];
    [_wxLoginBtn setImage:[UIImage imageNamed:@"weChat_login"] forState:UIControlStateNormal];
    [_wxLoginBtn addTarget:weakSelf action:@selector(wechatthirdLogin) forControlEvents:UIControlEventTouchUpInside];
    [wxLoginView addSubview:_wxLoginBtn];
    
    [_wxLoginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@52);
        make.height.equalTo(@52);
        make.centerX.centerY.mas_equalTo(wxLoginView);
    }];
    
    UIView *akeyLoginView = [UIView new];
    [stackView addArrangedSubview:akeyLoginView];
    
    [akeyLoginView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@52);
    }];
    
    UIButton *akeyLoginBtn = [UIButton new];
    [akeyLoginBtn setImage:[UIImage imageNamed:@"akey_login"] forState:UIControlStateNormal];
    [akeyLoginBtn addTapGesture:^(UIView *label) {
        weakSelf.isOnePush = 0;
        [weakSelf yijianLoginBtnAction];
    }];
    [akeyLoginView addSubview:akeyLoginBtn];
    
    [akeyLoginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@52);
        make.height.equalTo(@52);
        make.centerX.centerY.mas_equalTo(akeyLoginView);
    }];
    
    UIView *appleLoginView = [UIView new];
    [stackView addArrangedSubview:appleLoginView];
    
    [appleLoginView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@52);
    }];
    
    UIButton *appleLoginBtn = [UIButton new];
    [appleLoginBtn setImage:[UIImage imageNamed:@"apple_icon"] forState:UIControlStateNormal];
    [appleLoginBtn addTapGesture:^(UIView *label) {
        [weakSelf authorizationAppleID];
    }];
    [appleLoginView addSubview:appleLoginBtn];
    
    [appleLoginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@52);
        make.height.equalTo(@52);
        make.centerX.centerY.mas_equalTo(appleLoginView);
    }];
    
    
//    if(!_weChatInstall){
//        wxLoginView.hidden = YES;
//    }
    wxLoginView.hidden = YES;
    appleLoginView.hidden = YES;
    
    weakSelf.isSelect = NO;
    
    UIButton *agreBtn = [UIButton new];
    [agreBtn addUpInside:^(UIButton *button) {
        DLogFunc
        weakSelf.isSelect = !weakSelf.isSelect;
        [weakSelf protocolIsSelect:weakSelf.isSelect];
    }];
    [weakSelf.view addSubview:agreBtn];
    
    [agreBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(weakSelf.view.mas_left);
        make.centerY.mas_equalTo(weakSelf.agressLabel);
        make.height.offset(52);
        make.width.offset(100);
    }];
    
    [weakSelf protocolIsSelect:weakSelf.isSelect];
}

- (void)protocolIsSelect:(BOOL)isSelect{
    //设置整段字符串的颜色
    NSDictionary *attributes = @{NSFontAttributeName:[UIFont PingFangSCRegular:11], NSForegroundColorAttributeName:UIColorWhite};
    
    NSMutableAttributedString *text = [[NSMutableAttributedString alloc] initWithString:@"  登录即代表同意《用户协议》《隐私政策》" attributes:attributes];
    //设置高亮色和点击事件
    MJWeakSelf
    [text yy_setTextHighlightRange:[[text string] rangeOfString:@"《用户协议》"] color:UIColorWhite backgroundColor:[UIColor clearColor] tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        NSLog(@"点击了《用户协议》");
        GeneralWebViewController *activeWeb=[[GeneralWebViewController alloc]init];
        activeWeb.url = agreementUrl;
        [weakSelf.navigationController pushViewController:activeWeb animated:YES];
    }];
    //设置高亮色和点击事件
    [text yy_setTextHighlightRange:[[text string] rangeOfString:@"《隐私政策》"] color:UIColorWhite backgroundColor:[UIColor clearColor] tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
        NSLog(@"点击了《隐私政策》");
        GeneralWebViewController *activeWeb=[[GeneralWebViewController alloc]init];
        activeWeb.url = privacyUrl;
        [weakSelf.navigationController pushViewController:activeWeb animated:YES];
    }];
    //添加图片
    UIImage *image = [UIImage imageNamed:self.isSelect == NO ? @"unSelectIcon" : @"selectIcon"];
    NSMutableAttributedString *attachment = [NSMutableAttributedString yy_attachmentStringWithContent:image contentMode:UIViewContentModeCenter attachmentSize:CGSizeMake(12, 12) alignToFont:[UIFont fontWithName:@"PingFangSC-Regular"  size:12] alignment:(YYTextVerticalAlignment)YYTextVerticalAlignmentCenter];
    //将图片放在最前面
    [text insertAttributedString:attachment atIndex:0];
//    //添加图片的点击事件
//    [text yy_setTextHighlightRange:[[text string] rangeOfString:[attachment string]] color:[UIColor clearColor] backgroundColor:[UIColor clearColor] tapAction:^(UIView * _Nonnull containerView, NSAttributedString * _Nonnull text, NSRange range, CGRect rect) {
//        __weak typeof(self) weakSelf = self;
//
//    }];
    _agressLabel.attributedText = text;
    //居中显示一定要放在这里，放在viewDidLoad不起作用
    _agressLabel.textAlignment = NSTextAlignmentCenter;
    [self istouchLoginBtn];
}


#pragma mark --短信登录
- (void)loginFunction{
    MJWeakSelf
    if([HYTools StringisEmpty:self.phoneTF.text])
    {
        [HYTools showAlertMsg:@"请输入手机号"];
    }
    else if([HYTools StringisEmpty:self.codeTF.text])
    {
        [HYTools showAlertMsg:@"请输入验证码"];
    }
    else if(self.isSelect == NO)
    {
        [HYTools showAlertMsg:@"请勾选协议再进行登录"];
    }else{
        setUserAccount(self.phoneTF.text);
        
        NSMutableDictionary *tempDic=[[NSMutableDictionary alloc]init];
        [tempDic setValue:[NSString stringWithFormat:@"%@",self.phoneTF.text] forKey:@"telephone"];
        [tempDic setValue:[NSString stringWithFormat:@"%@",self.codeTF.text] forKey:@"code"];
        [tempDic setValue:@(2) forKey:@"logintype"];//登录类型 1：密码 2：短信
        //            [tempDic setValue:@(2) forKey:@"client"];//推送时采用的标识码
        [tempDic setValue:getClientId forKey:@"clientid"];
        [tempDic setValue:@"2" forKey:@"reg_from"];
        
        [QMUITips showLoadingInView:weakSelf.view];
        [[NetWorkTool sharedTools]requestMethods:POST URL:getComLoginUrl parameters:tempDic andBeingLoaded:NO andCache:NO success:^(id responseObject){
            [weakSelf endViewEditing];
            NSString * securitycheckval = StringIsNull(responseObject[@"data"][@"token"]);
            setUserID(responseObject[@"data"][@"uid"]);
            [HYTools GeTuiSdkUnbindAlias];
            setToken(securitycheckval);
            DLog(@"---11111------%@------%@",responseObject[@"data"][@"uid"],securitycheckval);
            Synchron;
             int result = [HYTools compareOneDay:[NSDate dateWithStringMuitiform:responseObject[@"data"][@"created_time"]] withAnotherDay:weakSelf.intoDate];
            if(result >= 0 && GETIDFAINFO.length) {
                [weakSelf douyinReportActiveRegister];
                [weakSelf baiduReportActiveRegister];
            }
            [QMUITips hideAllTips];
            weakSelf.view.userInteractionEnabled = NO;
            UIWindow *window = [UIApplication sharedApplication].windows[0];
            [weakSelf getUserInfoData:^{
                [QMUITips showSucceed:@"登录成功" inView:window hideAfterDelay:1.5f];
                
                
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [weakSelf returnToRoot];
                });
            }];
           
        } failure:^(NSError *error) {
//            [QMUITips hideAllTips];
        }];
    }
}

#pragma mark- 授权苹果ID
- (void)authorizationAppleID{
    if ([NetWorkConnect integerValue]==0){
        [HYTools showAlertMsg:@"网络请求失败" Duration:2.0];
        return;
    }
    if (self.isSelect == NO){
        [HYTools showAlertMsg:@"请先勾选协议" Duration:2.0];
        return;
    }
    if (@available(iOS 13.0, *)) {
        // 基于用户的Apple ID授权用户，生成用户授权请求的一种机制
        ASAuthorizationAppleIDProvider * appleIDProvider = [[ASAuthorizationAppleIDProvider alloc] init];
        // 创建新的AppleID 授权请求
        ASAuthorizationAppleIDRequest * authAppleIDRequest = [appleIDProvider createRequest];
        // 在用户授权期间请求的联系信息
//        authAppleIDRequest.requestedScopes = @[ASAuthorizationScopeFullName, ASAuthorizationScopeEmail];
        //如果 KeyChain 里面也有登录信息的话，可以直接使用里面保存的用户名和密码进行登录。
//        ASAuthorizationPasswordRequest * passwordRequest = [[[ASAuthorizationPasswordProvider alloc] init] createRequest];

        NSMutableArray <ASAuthorizationRequest *> * array = [NSMutableArray arrayWithCapacity:2];
        if (authAppleIDRequest) {
            [array addObject:authAppleIDRequest];
        }
//        if (passwordRequest) {
//            [array addObject:passwordRequest];
//        }
        NSArray <ASAuthorizationRequest *> * requests = [array copy];
        // 由ASAuthorizationAppleIDProvider创建的授权请求 管理授权请求的控制器
        ASAuthorizationController * authorizationController = [[ASAuthorizationController alloc] initWithAuthorizationRequests:requests];
         // 设置授权控制器通知授权请求的成功与失败的代理
        authorizationController.delegate = self;
        // 设置提供 展示上下文的代理，在这个上下文中 系统可以展示授权界面给用户
        authorizationController.presentationContextProvider = self;
        // 在控制器初始化期间启动授权流
        [authorizationController performRequests];
    }
}

#pragma mark- ASAuthorizationControllerDelegate
// 授权成功
- (void)authorizationController:(ASAuthorizationController *)controller didCompleteWithAuthorization:(ASAuthorization *)authorization API_AVAILABLE(ios(13.0)) {

    if ([authorization.credential isKindOfClass:[ASAuthorizationAppleIDCredential class]]) {

        ASAuthorizationAppleIDCredential * credential = (ASAuthorizationAppleIDCredential *)authorization.credential;

        // 苹果用户唯一标识符，该值在同一个开发者账号下的所有 App 下是一样的，开发者可以用该唯一标识符与自己后台系统的账号体系绑定起来。
        NSString *userID = credential.user;
        DLog(@"苹果登录:%@",userID);
        [self appleLoginValidation:userID];
        //把用户的唯一标识 传给后台 判断该用户是否绑定手机号，如果绑定了直接登录，如果没绑定跳绑定手机号页面
//        // 苹果用户信息 如果授权过，可能无法再次获取该信息
//        NSPersonNameComponents * fullName = credential.fullName;
//        NSString * email = credential.email;
//
//        // 服务器验证需要使用的参数
//        NSString * authorizationCode = [[NSString alloc] initWithData:credential.authorizationCode encoding:NSUTF8StringEncoding];
//        NSString * identityToken = [[NSString alloc] initWithData:credential.identityToken encoding:NSUTF8StringEncoding];
//
//        // 用于判断当前登录的苹果账号是否是一个真实用户，取值有：unsupported、unknown、likelyReal
//        ASUserDetectionStatus realUserStatus = credential.realUserStatus;

//        NSLog(@"userID: %@", userID);
//        NSLog(@"fullName: %@", fullName);
//        NSLog(@"email: %@", email);
//        NSLog(@"authorizationCode: %@", authorizationCode);
//        NSLog(@"identityToken: %@", identityToken);
//        NSLog(@"realUserStatus: %@", @(realUserStatus));
        
    }else if ([authorization.credential isKindOfClass:[ASPasswordCredential class]]) {
        // 这个获取的是iCloud记录的账号密码，需要输入框支持iOS 12 记录账号密码的新特性，如果不支持，可以忽略
        // 用户登录使用现有的密码凭证
        ASPasswordCredential * passwordCredential = (ASPasswordCredential *)authorization.credential;
        // 密码凭证对象的用户标识 用户的唯一标识
        NSString * user = passwordCredential.user;
        DLog(@"苹果登录:%@",user);
        [self appleLoginValidation:user];
        //把用户的唯一标识 传给后台 判断该用户是否绑定手机号，如果绑定了直接登录，如果没绑定跳绑定手机号页面

//        // 密码凭证对象的密码
//        NSString * password = passwordCredential.password;
//        NSLog(@"userID: %@", user);
//        NSLog(@"password: %@", password);

    } else {
        DLog(@"授权失败");
        [QMUITips showError:@"授权失败"];
    }
}

- (void)appleLoginValidation:(NSString *)userID{
    NSMutableDictionary *imforDic=[[NSMutableDictionary alloc]init];
    [imforDic setValue:userID forKey:@"apple_id"];
    MJWeakSelf
    [[NetWorkTool sharedTools]requestMethods:POST URL:appleLoginUrl parameters:imforDic andBeingLoaded:YES andCache:NO success:^(id responseObject) {
        NSDictionary *dic = responseObject[@"data"];
        [QMUITips hideAllTips];
//        0需绑定手机登录
        if ([dic[@"bind_phone"]integerValue] == 0){
            BindMobilePhoneViewController *bindMobileVC = [[BindMobilePhoneViewController alloc]init];
            bindMobileVC.appleId = userID;
            [weakSelf.navigationController pushViewController:bindMobileVC animated:YES];
        }else{
            setUserID(responseObject[@"data"][@"uid"]);
            setToken(responseObject[@"data"][@"token"]);
            Synchron;
            [HYTools GeTuiSdkUnbindAlias];
            dispatch_async(dispatch_get_main_queue(), ^{
                [QMUITips showSucceed:@"登录成功" inView:weakSelf.view hideAfterDelay:1.5f];
            });
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf returnToRoot];
            });
        }
    } failure:^(NSError *error) {
        DLog(@"123");
    }];
}

// 授权失败
- (void)authorizationController:(ASAuthorizationController *)controller didCompleteWithError:(NSError *)error API_AVAILABLE(ios(13.0)) {
    NSString *errorMsg = nil;
    switch (error.code) {
        case ASAuthorizationErrorCanceled:
            errorMsg = @"用户取消了授权请求";
            break;
        case ASAuthorizationErrorFailed:
            errorMsg = @"授权请求失败";
            break;
        case ASAuthorizationErrorInvalidResponse:
            errorMsg = @"授权请求响应无效";
            break;
        case ASAuthorizationErrorNotHandled:
            errorMsg = @"未能处理授权请求";
            break;
        case ASAuthorizationErrorUnknown:
            errorMsg = @"授权请求失败未知原因";
            break;
    }
    NSLog(@"%@", errorMsg);
}

#pragma mark- ASAuthorizationControllerPresentationContextProviding
- (ASPresentationAnchor)presentationAnchorForAuthorizationController:(ASAuthorizationController *)controller  API_AVAILABLE(ios(13.0)){
    return self.view.window;
}
#pragma mark- apple授权状态 更改通知
- (void)handleSignInWithAppleStateChanged:(NSNotification *)notification{
    NSLog(@"%@", notification.userInfo);
}

#pragma mark - 微信登录
- (void)wechatthirdLogin{
    if ([NetWorkConnect integerValue]==0){
        [HYTools showAlertMsg:@"网络请求失败"];
        return;
    }
    if (self.isSelect == NO){
        [HYTools showAlertMsg:@"请先勾选协议" Duration:2.0];
        return;
    }
    
    if(![WXApi isWXAppInstalled]){
        [CommonFunc showAlertViews:@"请输入手机号验证进行登录" message:@"" viewcontroller:self];
    }else{
        AppDelegate *Appgate=(AppDelegate*)[UIApplication sharedApplication].delegate;
        [Appgate WechatAuthoClieckForm:self];
    }
}

- (void)otherBtn{
    //关闭页面
    self.tabBarController.tabBar.hidden = YES;
    [CLShanYanSDKManager finishAuthControllerCompletion:^{
        //如需关闭后present/push新页面，建议在completion回调中执行
        //注：若未拉起授权页，调用此方法，block不会触发
        //用户跳转短信验证
    }];
}

#pragma mark - 一键登录
- (void)yijianLoginBtnAction{
    [QMUITips showLoading:@"请稍后..." inView:self.view];
    // 获取默认参数配置
    CLUIConfigure * baseUIConfigure = [CLUIConfigure clDefaultUIConfigure];
    baseUIConfigure.clLogoImage = [UIImage imageNamed:@"welcomewineyune"];
    baseUIConfigure.viewController = self;
    baseUIConfigure.clAuthWindowPresentingAnimate = @(NO);
    baseUIConfigure.clAuthWindowModalTransitionStyle = @(UIModalTransitionStyleCrossDissolve);
    // 是否需要手动销毁授权页面，默认自动销毁， YES->手动销毁
    //PhoneNumber
    baseUIConfigure.clPhoneNumberColor = UIColor.whiteColor;
    baseUIConfigure.clPhoneNumberFont = [UIFont boldPingFangSCOfSize:33];
    
    //LoginBtn
    UIBarButtonItem *saveButton = [[UIBarButtonItem alloc]
                                   initWithTitle:@"其他手机号登录"
                                   style:UIBarButtonItemStylePlain
                                   target:self
                                   action:@selector(otherBtn)];
    [saveButton setTitleTextAttributes:@{NSForegroundColorAttributeName: [UIColor whiteColor]}
                              forState:UIControlStateNormal];
    baseUIConfigure.manualDismiss = @(YES);
    baseUIConfigure.clNavigationBackBtnImage = [UIImage imageNamed:@"nav_back_white"];
    baseUIConfigure.clNavigationRightControl = saveButton;
    baseUIConfigure.clAppPrivacyWebBackBtnImage = [UIImage imageNamed:@"nav_back_black"];
    baseUIConfigure.clLoginBtnTextColor = [YDMyColors getLoginRedColor];
    baseUIConfigure.clNavigationBackgroundClear = @(YES);
    baseUIConfigure.clLoginBtnNormalBgImage = [UIImage imageNamed:@"loginBtn_N"];
    baseUIConfigure.clLoginBtnText = @"本机号码一键登录";
    baseUIConfigure.clShanYanSloganHidden = @(1);
    
    //Privacy
    baseUIConfigure.clAppPrivacyColor = @[[UIColor whiteColor],[UIColor whiteColor]];
    baseUIConfigure.clAppPrivacyTextAlignment = @(NSTextAlignmentCenter);
    baseUIConfigure.clAppPrivacyTextFont = [UIFont PingFangSCRegular:11];
    baseUIConfigure.clAppPrivacyPunctuationMarks = @(YES);
    baseUIConfigure.clAppPrivacyNormalDesTextFirst = @"登录即代表同意";
    baseUIConfigure.clAppPrivacyNormalDesTextLast = @"";
    baseUIConfigure.clNavigationBottomLineHidden = @(YES);
    baseUIConfigure.clAppPrivacyFirst = @[@"《用户协议》",agreementUrl];
    baseUIConfigure.clAppPrivacySecond = @[@"《隐私政策》",privacyUrl];
    MJWeakSelf
    [baseUIConfigure setCheckBoxTipView:^(UIView * _Nonnull containerView) {
        [HYTools showAlertMsg:@"请先勾选协议"];
    }];
//    
    // 控件布局设置
    CGFloat screenScale = [UIScreen mainScreen].bounds.size.width/375.0;
    if (screenScale > 1) {
        screenScale = 1;
    }
    
//    baseUIConfigure.customAreaView = ^(UIView * _Nonnull customAreaView) {
//        UIButton *otherLoginWayBtn = [[UIButton alloc]init];
//        [otherLoginWayBtn.titleLabel setFont:[UIFont PingFangSCRegular:14]];
//        [otherLoginWayBtn setTitle:@"其他方式登录" forState:UIControlStateNormal];
//        otherLoginWayBtn.titleLabel.textAlignment = NSTextAlignmentCenter;
//        
//        //根据字体得到尺寸
//        CGSize otherLoginWayBtnSize = [otherLoginWayBtn.titleLabel.text sizeWithAttributes:[NSDictionary dictionaryWithObjectsAndKeys:[UIFont PingFangSCRegular:14],NSFontAttributeName,nil]];
//        CGFloat otherLoginWayBtnOffsetX =  0.95 * (ScreenWidth-otherLoginWayBtnSize.width*screenScale);
//        CGFloat otherLoginWayBtnOffsetY = 50*screenScale;
//        otherLoginWayBtn.frame = CGRectMake(otherLoginWayBtnOffsetX, otherLoginWayBtnOffsetY, otherLoginWayBtnSize.width*screenScale, otherLoginWayBtnSize.height*screenScale);
//        [otherLoginWayBtn setTitleColor:UIColorWhite forState:UIControlStateNormal];
//        [customAreaView addSubview:otherLoginWayBtn];
//        [otherLoginWayBtn addTarget:weakSelf action:@selector(otherBtn) forControlEvents:UIControlEventTouchUpInside];
//    };

    //背景
    baseUIConfigure.clBackgroundImg = UIImageMake(@"login_back_img");
    
    //CheckBox
//    baseUIConfigure.clCheckBoxHidden = @(NO);
    baseUIConfigure.clCheckBoxValue = @(NO);
    baseUIConfigure.clCheckBoxSize = [NSValue valueWithCGSize:CGSizeMake(60, 60)];
    
//    baseUIConfigure.clCheckBoxImageEdgeInsets = [NSValue valueWithUIEdgeInsets:UIEdgeInsetsMake(2, 2, 2, 2)];
    baseUIConfigure.clCheckBoxVerticalAlignmentToAppPrivacyCenterY = @(YES);
    baseUIConfigure.clCheckBoxCheckedImage = [UIImage imageNamed:@"selectIcon"];
//    baseUIConfigure.clCheckBoxVerticalAlignmentOffset = @(-8);
    baseUIConfigure.clCheckBoxUncheckedImage = [UIImage imageNamed:@"unSelectIcon"];
    //Slogan
    baseUIConfigure.clCheckBoxImageEdgeInsets = [NSValue valueWithUIEdgeInsets:UIEdgeInsetsMake(0, 30, 16, 0)];
    baseUIConfigure.clCheckBoxVerticalAlignmentToAppPrivacyTop = @(YES);
    baseUIConfigure.clSloganTextColor = UIColor.whiteColor;
    baseUIConfigure.clSloganTextFont = [UIFont PingFangSCRegular:14];
    baseUIConfigure.clSlogaTextAlignment = @(NSTextAlignmentCenter);
    
    //layout 布局
    //布局-竖屏
    CLOrientationLayOut * clOrientationLayOutPortrait = [CLOrientationLayOut new];

    clOrientationLayOutPortrait.clLayoutLogoWidth = @(171*screenScale);
    clOrientationLayOutPortrait.clLayoutLogoHeight = @(136*screenScale);
    clOrientationLayOutPortrait.clLayoutLogoCenterX = @(0);
    clOrientationLayOutPortrait.clLayoutLogoTop = @(20+NavigationContentTop);
    
    clOrientationLayOutPortrait.clLayoutPhoneTop = @(257+NavigationContentTop);
    clOrientationLayOutPortrait.clLayoutPhoneLeft = @(50*screenScale);
    clOrientationLayOutPortrait.clLayoutPhoneRight = @(-50*screenScale);
    clOrientationLayOutPortrait.clLayoutPhoneHeight = @(46*screenScale);
    
    clOrientationLayOutPortrait.clLayoutSloganLeft = @(0);
    clOrientationLayOutPortrait.clLayoutSloganRight = @(0);
    clOrientationLayOutPortrait.clLayoutSloganHeight = @(15*screenScale);
    clOrientationLayOutPortrait.clLayoutSloganTop = @(307+NavigationContentTop);

    clOrientationLayOutPortrait.clLayoutLoginBtnTop= @(364+NavigationContentTop);
    clOrientationLayOutPortrait.clLayoutLoginBtnHeight = @(50*screenScale);
    clOrientationLayOutPortrait.clLayoutLoginBtnLeft = @(26*screenScale);
    clOrientationLayOutPortrait.clLayoutLoginBtnRight = @(-26*screenScale);

//    clOrientationLayOutPortrait.clLayoutAppPrivacyLeft = @(54*screenScale);
//    clOrientationLayOutPortrait.clLayoutAppPrivacyRight = @(-54*screenScale);
//    clOrientationLayOutPortrait.clLayoutAppPrivacyCenterX = @(0);
//    clOrientationLayOutPortrait.clLayoutAppPrivacyBottom = @(-25*screenScale);
    clOrientationLayOutPortrait.clLayoutAppPrivacyLeft = @(screenScale*0.125 + 50);
    clOrientationLayOutPortrait.clLayoutAppPrivacyRight = @(-screenScale*0.125 -30);
    clOrientationLayOutPortrait.clLayoutAppPrivacyBottom = @(clOrientationLayOutPortrait.clLayoutSloganBottom.floatValue - clOrientationLayOutPortrait.clLayoutSloganHeight.floatValue);
    clOrientationLayOutPortrait.clLayoutAppPrivacyHeight = @(50);

    baseUIConfigure.clOrientationLayOutPortrait = clOrientationLayOutPortrait;
    //闪验一键登录接口（将拉起授权页）
    [CLShanYanSDKManager quickAuthLoginWithConfigure:baseUIConfigure openLoginAuthListener:^(CLCompleteResult * _Nonnull completeResult) {
        [QMUITips hideAllTipsInView:weakSelf.view];
        if (weakSelf.isOnePush != 0) {
            [weakSelf bottomInit];
        }
        // hide loading...
        if (completeResult.error) {
            //拉起授权页失败
            NSLog(@"openLoginAuthListener1:%@",completeResult.error.userInfo);
            if (weakSelf.isOnePush == 0){
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, 1 * NSEC_PER_SEC), dispatch_get_main_queue(), ^{
                    if (completeResult.error.userInfo[@"resultMsg"]) {
                        [HYTools showAlertMsg:HMSTR(@"%@",completeResult.error.userInfo[@"resultMsg"]) Duration:Alert_ServerErrorShowduration];
                    }else if (completeResult.error.userInfo[@"retMsg"]) {
                        [HYTools showAlertMsg:HMSTR(@"%@",completeResult.error.userInfo[@"retMsg"]) Duration:Alert_ServerErrorShowduration];
                    }else if (completeResult.error.userInfo[@"msg"]) {
                        [HYTools showAlertMsg:HMSTR(@"%@",completeResult.error.userInfo[@"msg"]) Duration:Alert_ServerErrorShowduration];
                    }else{
                        if (completeResult.error.userInfo[@"desc"]) {
                            [HYTools showAlertMsg:HMSTR(@"%@",completeResult.error.userInfo[@"desc"]) Duration:Alert_ServerErrorShowduration];
                        }else{
                            [HYTools showAlertMsg:HMSTR(@"%@",@"请求网络失败,请检查你的网络是否连接!") Duration:Alert_ServerErrorShowduration];
                        }
                    }
                });
            }
        }else{
            //拉起授权页成功
            DLog(@"openLoginAuthListener2:%@",completeResult.data);
        }
    } oneKeyLoginListener:^(CLCompleteResult * _Nonnull completeResult) {
        if (completeResult.error) {
            //一键登录失败
            DLog(@"oneKeyLoginListener:%@",completeResult.error.description);
            //提示：错误无需提示给用户，可以在用户无感知的状态下直接切换登录方式
            if (completeResult.code == 1011){
                //用户取消登录（点返回）
                //处理建议：如无特殊需求可不做处理，仅作为交互状态回调，此时已经回到当前用户自己的页面
                //点击sdk自带的返回，无论是否设置手动销毁，授权页面都会强制关闭
                if (weakSelf.isOnePush == 1) {
                    [weakSelf bottomInit];
                    if([getEnterGuide integerValue]==1){
//                        setEnterGuide(@"0");
//                        Synchron;
                        YDTabBarViewController *tabBar = [[YDTabBarViewController alloc]init];
                        CATransition *transtition = [CATransition animation];
                        transtition.duration = 0.5;
                        transtition.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut];
                        [[NSNotificationCenter defaultCenter] postNotificationName:ME_PAGE_DID_CHANGE_NOTIFICATION object:nil];
                        [[NSNotificationCenter defaultCenter] postNotificationName:COMMUNITY_HOME_REFRESH_NOTIFICATION object:nil];
                        [[NSNotificationCenter defaultCenter] postNotificationName:MESSAGE_DATA_NUMBER_REFRESH_NOTIFICATION object:nil];
                        [UIApplication sharedApplication].windows[0].rootViewController = tabBar;
                        [[UIApplication sharedApplication].windows[0].layer addAnimation:transtition forKey:@"animation"];
                    }else{
                        NSInteger index = weakSelf.navigationController.viewControllers.count - 2;
                        if (index < 0) {
                            index = 0;
                        }
                        [weakSelf.navigationController popToViewController:weakSelf.navigationController.viewControllers[index] animated:YES];
                        [CLShanYanSDKManager finishAuthControllerCompletion:nil];
                    }
                }
            }  else{
                //处理建议：其他错误代码表示闪验通道无法继续，可以统一走开发者自己的其他登录方式，也可以对不同的错误单独处理
                //关闭授权页，如果授权页还未拉起，此调用不会关闭当前用户vc，即不执行任何代码
                [CLShanYanSDKManager finishAuthControllerCompletion:nil];
            }
        }else{
            //一键登录获取Token成功
            //关闭页面
            [CLShanYanSDKManager finishAuthControllerCompletion:^{
                //如需关闭后present/push新页面，建议在completion回调中执行
                //注：若未拉起授权页，调用此方法，block不会触发
                //用户跳转短信验证
                DLog(@"oneKeyLoginListener:%@",completeResult.data);
                
                NSMutableDictionary *tempDic = [[NSMutableDictionary alloc]init];
                [tempDic setValue:HMSTR(@"%@",completeResult.data[@"token"]) forKey:@"token"];
                [tempDic setValue:getClientId forKey:@"clientid"];
                [tempDic setValue:@"2" forKey:@"reg_from"];
                [tempDic setValue:@"6666" forKey:@"ios_test"];
                
                [QMUITips showLoadingInView:weakSelf.view];
                [[NetWorkTool sharedTools]requestMethods:POST URL:acquirePhoneForCLUrl parameters:tempDic andBeingLoaded:NO andCache:NO success:^(id responseObject) {
                    NSString * securitycheckval = StringIsNull(responseObject[@"data"][@"token"]);
                    setUserID(responseObject[@"data"][@"uid"]);
                    [HYTools GeTuiSdkUnbindAlias];
                    setToken(securitycheckval);
                    DLog(@"---11111------%@------%@",responseObject[@"data"][@"uid"],securitycheckval);
                    Synchron;
                    DLog(@"第三方:%@",getUserID);
                    int result = [HYTools compareOneDay:[NSDate dateWithStringMuitiform:responseObject[@"data"][@"created_time"]] withAnotherDay:weakSelf.intoDate];
                    if(result >= 0 && GETIDFAINFO.length) {
                        [weakSelf douyinReportActiveRegister];
                        [weakSelf baiduReportActiveRegister];
                    }
                    [QMUITips hideAllTips];
                    weakSelf.view.userInteractionEnabled = NO;
                    UIWindow *window = [UIApplication sharedApplication].windows[0];
                    [weakSelf getUserInfoData:^{
                        [QMUITips showSucceed:@"登录成功" inView:window hideAfterDelay:1.5f];
                        
                        
                        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                            [weakSelf returnToRoot];
                        });
                    }];
                } failure:^(NSError *error) {
//                    [QMUITips showError:@"登录失败" inView:weakSelf.view hideAfterDelay:1.5];
                }];
            }];
            //SDK成功获取到Token
            /** token置换手机号
             code
             */
        }
    }];
}

#pragma mark --- 获取验证码
- (void)getPhoneCode:(UIButton *)sender{
    if([HYTools StringisEmpty:self.phoneTF.text])
    {
        [CommonFunc showAlertViews:@"提示" message:@"请输入手机号" viewcontroller:self];
        return ;
    }
    if (![HYTools checkTelNumber:self.phoneTF.text]) {
        [CommonFunc showAlertViews:@"提示" message:@"手机号格式不正确！" viewcontroller:self];
        return;
    }

    // 检查是否勾选协议
    if (self.isSelect == NO) {
        [self showAgreementAlert];
        return;
    }

    [self performGetCodeAction];
}

// 显示温馨提示框
- (void)showAgreementAlert {
    MJWeakSelf
    QMUIAlertController *alertController = [QMUIAlertController alertControllerWithTitle:@"温馨提示" message:@"请先阅读并同意《用户协议》和《隐私政策》" preferredStyle:QMUIAlertControllerStyleAlert];

    QMUIAlertAction *cancelAction = [QMUIAlertAction actionWithTitle:@"取消" style:QMUIAlertActionStyleCancel handler:nil];

    QMUIAlertAction *agreeAction = [QMUIAlertAction actionWithTitle:@"同意并登录" style:QMUIAlertActionStyleDefault handler:^(__kindof QMUIAlertController *aAlertController, QMUIAlertAction *action) {
        // 自动勾选协议
        weakSelf.isSelect = YES;
        [weakSelf protocolIsSelect:weakSelf.isSelect];
        // 执行获取验证码操作
        [weakSelf performGetCodeAction];
    }];

    [alertController addAction:cancelAction];
    [alertController addAction:agreeAction];
    [alertController showWithAnimated:YES];
}

// 执行获取验证码操作
- (void)performGetCodeAction {
    [self endViewEditing];
    WKWebViewConfiguration * configuration = [[WKWebViewConfiguration alloc] init];
    [configuration.userContentController addScriptMessageHandler:[[WeakScriptMessageDelegate alloc] initWithDelegate:self] name:@"getTickit"];
    self.webview = [[WKWebView alloc]initWithFrame:CGRectMake(0.0, 0, SCREEN_WIDTH, SCREEN_HEIGHT+30) configuration:configuration];
    self.webview.UIDelegate = self;
    self.webview.userInteractionEnabled = YES;
    self.webview.navigationDelegate = self;
    [self.view addSubview:self.webview];
    self.webview.backgroundColor = [UIColor clearColor];
    [self.webview setOpaque:NO];
    self.webview.scrollView.scrollEnabled = NO;
    [self.webview loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:jsloginUrl]]];
    //https://h5.wine-talk.cn/web-static/jslogin.html
    //    [[self.webview  configuration].userContentController addScriptMessageHandler:self name:@"Consol_logger"];
}

// 页面开始加载时调用
- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation {
    [[UIApplication sharedApplication] setNetworkActivityIndicatorVisible:YES];
    [QMUITips showLoading:@"请稍后..." inView:self.view];
}

// 页面加载完成之后调用
- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    [[UIApplication sharedApplication] setNetworkActivityIndicatorVisible:NO];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [QMUITips hideAllTips];
    });
}
// 页面加载失败时调用
- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(null_unspecified WKNavigation *)navigation withError:(NSError *)error {
    [[UIApplication sharedApplication] setNetworkActivityIndicatorVisible:NO];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [QMUITips hideAllTips];
    });
}

#pragma mark WKNavigationDelegate
- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(nonnull WKNavigationAction *)navigationAction decisionHandler:(nonnull void (^)(WKNavigationActionPolicy))decisionHandler{
    NSURL *URL = navigationAction.request.URL;
    NSString * currentURL = [[URL absoluteString] stringByRemovingPercentEncoding];
    DLog(@"the currentURL is:%@ %ld",currentURL,navigationAction.navigationType);
    //    NSRange ranges=[currentURL rangeOfString:@"AppActive/getCoupon"];//
    //    NSRange ranges1 = [currentURL rangeOfString:@"javasript:void(0)"];
    decisionHandler(WKNavigationActionPolicyAllow); // 必须实现 加载
    return;
}
//https://captcha.253.com/cap_union_new_show?aid=2096409785&protocol=https&accver=1&showtype=popup&ua=TW96aWxsYS81LjAgKGlQaG9uZTsgQ1BVIGlQaG9uZSBPUyAxNV8xIGxpa2UgTWFjIE9TIFgpIEFwcGxlV2ViS2l0LzYwNS4xLjE1IChLSFRNTCwgbGlrZSBHZWNrbykgTW9iaWxlLzE1RTE0OA==&noheader=0&fb=0&aged=0&enableAged=0&enableDarkMode=0&grayscale=1&clientype=1&sess=s0BEvq4f783KfEBryl0wBl6_bc0VEr8PwSgS14vAZpdv4axitsoCXjKR65EUaLfIV_1_kx_BMrl1BH0SBK38GLHoUbI0spJBSW_hmt0TitWF_G3Q5ftzi4Z6zpzVA7ifgCp5wcrqkOaQXmlcY0n4Iy8tqouUsEmF_tE5rP9F9BILztOYC1aGxeV1Gum5v-2NyDWAHvEx6aF2DPcG0Z2bu2KuXb7JFZej_f2I-nQkZWyz0NGTBbaJi90zYWQhMB8mmn&fwidth=0&sid=6940949387113684993&wxLang=&tcScale=1&uid=&cap_cd=&rnd=28003&prehandleLoadTime=16489&createIframeStart=1654851290544&global=0&subsid=2
//https://test-wine.wineyun.com/html-statics/view/h5Login.html
//https://captcha.253.com/template/drag_ele.html?t=1654851273774
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    NSLog(@"name = %@, body = %@", message.name, message.body);
    MJWeakSelf
    if ([message.name caseInsensitiveCompare:@"getTickit"] == NSOrderedSame) {
        if (message.body) {
            NSData *jsonData = [message.body dataUsingEncoding:NSUTF8StringEncoding];
            NSError *err;
            NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                options:NSJSONReadingMutableContainers
                                                                  error:&err];
            if (!err) {
                [self.webview removeFromSuperview];
                [self.userContentController removeScriptMessageHandlerForName:@"getTickit"];
                if ([dic[@"status"] isEqualToString:@"success"]) {
                    //图形验证成功 发送短信验证码
                    if(self.codeNumber == 60){
                        self.codeTimer =[NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(cycleTimeOutAction:) userInfo:nil repeats:YES];
                        [self codeAvailable:NO];
                        NSMutableDictionary *tempDic=[[NSMutableDictionary alloc]init];
                        [tempDic setValue:[NSString stringWithFormat:@"%@",self.phoneTF.text] forKey:@"telephone"];
                        [tempDic setValue:HMSTR(@"%@",dic[@"ticket"]) forKey:@"ticket"];
                        [tempDic setValue:HMSTR(@"%@",dic[@"randstr"]) forKey:@"randstr"];
                        [[NetWorkTool sharedTools]requestMethods:POST URL:sendRegCodeUrl parameters:tempDic andBeingLoaded:YES andCache:NO success:^(id responseObject) {
                            // 跳转到验证码输入页面
                            [weakSelf pushToCodeInputPage];
                        } failure:^(NSError *error) {
                            [weakSelf cancelphonecode];
                        }];
                    }
                }
            }
        }
    }else if ([message.name isEqualToString:@"Consol_logger"]) {
        NSLog(@"message = %@",message.body);
    }
}

- (void)userDelegateBtnAction{
    GeneralWebViewController *activeWeb=[[GeneralWebViewController alloc]init];
    activeWeb.url = privacyUrl;
    [self.navigationController pushViewController:activeWeb animated:YES];
}

//倒计时
- (void)cycleTimeOutAction:(id)sender{
    [self.getCodeBtn setTitle:[NSString stringWithFormat:@"%lds",(long)self.codeNumber] forState:UIControlStateNormal];
    self.codeNumber = self.codeNumber - 1;
    if(self.codeNumber == -1){
        [self cancelphonecode];
    }
}

//重新获取验证码
- (void)cancelphonecode{
    [self.codeTimer invalidate];
    self.codeTimer = nil;
    [self codeAvailable:YES];
    [self.getCodeBtn setTitle:@"重新获取" forState:UIControlStateNormal];
    self.codeNumber = 60;
}

//验证码按钮是否可用
- (void)codeAvailable:(BOOL)isCode{
    if (isCode == YES) {
        self.getCodeBtn.userInteractionEnabled = YES;
        [self.getCodeBtn setTitleColor:[YDMyColors getMainRedColor] forState:UIControlStateNormal];
        [self.getCodeBtn setBackgroundColor:UIColorWhite];
    }else{
        self.getCodeBtn.userInteractionEnabled = NO;
        [self.getCodeBtn setTitleColor:UIColorWhite forState:UIControlStateNormal];
        [self.getCodeBtn setBackgroundColor:UIColorMakeWithHex(@"#E58E93")];
    }
}


#pragma mark  -监听uitextfield的值得变化
- (void)textFieldDidChange:(UITextField *)textField{
    [self istouchLoginBtn];
}

- (void)istouchLoginBtn{
    if (self.codeNumber == 60) {
        if (self.phoneTF.text.length > 0) {
            [self codeAvailable:YES];
        }else{
            [self codeAvailable:NO];
        }
    }
    if (self.phoneTF.text.length > 10 && self.codeTF.text.length > 5 && self.isSelect == YES) {
        [self.loginButton setBackgroundColor:UIColorWhite];
        [self.loginButton setTitleColor:[YDMyColors getMainRedColor] forState:UIControlStateNormal];
    }else{
        [self.loginButton setBackgroundColor:UIColorMakeWithHex(@"#E58E93")];
        [self.loginButton setTitleColor:UIColorWhite forState:UIControlStateNormal];
    }
}

#pragma mark UIScrollViewDelegate
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView{
    [self endViewEditing];
}


//获取用户信息
- (void)getUserInfoData:(void(^)(void))callBack{
    MJWeakSelf
    [[NetWorkTool sharedTools]requestMethods:GET URL:getUserAllInfoUrl parameters:nil andBeingLoaded:NO andCache:NO success:^(id responseObject) {
        //用户信息
        NSDictionary *dic =[responseObject objectForKey:@"data"];
        UserInfo *currentInfo = [UserInfo mj_objectWithKeyValues:dic];
        [[UserInforObject userSingleton] saveUserInfo:currentInfo];
        setIsNewUser(dic[@"is_new_user"]);
        setCreatedDate(dic[@"created_date"]);
        NSArray *ary = dic[@"protocol"];
        if (ary.count > 0) {
            setIsCheckAgreement(ary[0]);
        }else{
            setIsCheckAgreement(@"0");
        }
        Synchron;
        [[NSNotificationCenter defaultCenter] postNotificationName:@"flashDeliveryRequestData" object:nil];
        if (callBack) {
             callBack();
        }
    } failure:^(NSError *error) {
        if (callBack) {
             callBack();
        }
    }];
}


- (void)returnToRoot{
    if([getEnterGuide integerValue]==1){
//        setEnterGuide(@"0");
//        Synchron;
        setClickBottomDelete(@(0));
        YDTabBarViewController *tabBar = [[YDTabBarViewController alloc]init];
        CATransition *transtition = [CATransition animation];
        if(![HYTools changeUidIsNull]) {
            tabBar.selectedIndex = 3;
        } else {
//            [[NSNotificationCenter defaultCenter] postNotificationName:@"updateBottomView" object:nil];
        }
        transtition.duration = 0.5;
        transtition.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut];
        [[NSNotificationCenter defaultCenter] postNotificationName:ME_PAGE_DID_CHANGE_NOTIFICATION object:nil];
        [[NSNotificationCenter defaultCenter] postNotificationName:COMMUNITY_HOME_REFRESH_NOTIFICATION object:nil];
        [[NSNotificationCenter defaultCenter] postNotificationName:MESSAGE_DATA_NUMBER_REFRESH_NOTIFICATION object:nil];
        [UIApplication sharedApplication].windows[0].rootViewController = tabBar;
        [[UIApplication sharedApplication].windows[0].layer addAnimation:transtition forKey:@"animation"];
    }else{
        setClickBottomDelete(@(0));
        [[NSNotificationCenter defaultCenter] postNotificationName:ME_PAGE_DID_CHANGE_NOTIFICATION object:nil];
        [[NSNotificationCenter defaultCenter] postNotificationName:COMMUNITY_HOME_REFRESH_NOTIFICATION object:nil];
        [[NSNotificationCenter defaultCenter] postNotificationName:MESSAGE_DATA_NUMBER_REFRESH_NOTIFICATION object:nil];
        [self.navigationController popViewControllerAnimated:YES];
//        [[NSNotificationCenter defaultCenter] postNotificationName:@"updateBottomView" object:nil];
    }
}

/// 抖音注册上传
- (void)douyinReportActiveRegister {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"idfa"] = GETIDFAINFO;
    params[@"os"] = @(1);
    params[@"event_type"] = @"active_register";
    [[NetWorkTool sharedTools] requestMethods:POST URL:douyinReportUrl parameters:params andBeingLoaded:NO andCache:NO success:^(id responseObject) {
       
    } failure:^(NSError *error) {
       
    }];
    
}

/// 百度注册上传
- (void)baiduReportActiveRegister {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"idfa"] = GETIDFAINFO;
    params[@"os"] = @(1);
    params[@"event_type"] = @"register";
    [[NetWorkTool sharedTools] requestMethods:POST URL:baiduReportUrl parameters:params andBeingLoaded:NO andCache:NO success:^(id responseObject) {
       
    } failure:^(NSError *error) {
       
    }];
    
}

- (BOOL)shouldAutorotate {
    return NO;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationPortrait;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIInterfaceOrientationMaskPortrait;
}

// 跳转到验证码输入页面
- (void)pushToCodeInputPage {
    MJWeakSelf
    CodeInputViewController *codeInputVC = [[CodeInputViewController alloc] init];
    codeInputVC.phoneNumber = self.phoneTF.text;
    codeInputVC.LoginSuccess = ^(CodeInputViewController *codeInputVC) {
        if (weakSelf.LoginSuccess) {
            weakSelf.LoginSuccess(weakSelf);
        } else {
            [weakSelf returnToRoot];
        }
    };
    [self.navigationController pushViewController:codeInputVC animated:YES];
}

@end
