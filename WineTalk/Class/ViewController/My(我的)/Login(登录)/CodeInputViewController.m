//
//  CodeInputViewController.m
//  WineTalk
//
//  Created by AI Assistant on 2024/12/19.
//

#import "CodeInputViewController.h"
#import "YDTabBarViewController.h"
#import <IQKeyboardManager/IQKeyboardManager.h>
#import <WebKit/WebKit.h>
#import "WeakScriptMessageDelegate.h"

@interface CodeInputViewController ()<UITextFieldDelegate, QMUITextFieldDelegate, WKUIDelegate, WKNavigationDelegate, WKScriptMessageHandler>

@property (nonatomic, strong) NSTimer *codeTimer;
@property (nonatomic, assign) NSInteger codeNumber;
@property (nonatomic, strong) QMUITextField *codeTF;
@property (nonatomic, strong) UIButton *getCodeBtn;
@property (nonatomic, strong) UIButton *loginButton;
@property (nonatomic, strong) UILabel *phoneLabel;
@property (nonatomic, strong) WKWebView *webview;
@property (nonatomic, strong) WKUserContentController* userContentController;

@end

@implementation CodeInputViewController

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
    [IQKeyboardManager sharedManager].enable = NO;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [IQKeyboardManager sharedManager].enable = YES;
    [self.codeTimer invalidate];
    self.codeTimer = nil;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.codeNumber = 60;
    self.view.backgroundColor = UIColorWhite;
    [self layOutSubViews];
    [self.codeTF becomeFirstResponder];
}

- (void)layOutSubViews {
    MJWeakSelf
    
    UIImageView *bgImageView = [UIImageView new];
    bgImageView.userInteractionEnabled = YES;
    bgImageView.image = UIImageMake(@"login_back_img");
    [self.view addSubview:bgImageView];

    [bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.right.mas_equalTo(self.view);
    }];
    
    UIButton *backBtn = [[UIButton alloc] initWithFrame:CGRectMake(15, SafeTop+13, 24, 24)];
    [backBtn setImage:[UIImage imageNamed:@"avcBackIcon"] forState:UIControlStateNormal];
    [backBtn addUpInside:^(UIButton *button) {
        [weakSelf.navigationController popViewControllerAnimated:YES];
    }];
    [self.view addSubview:backBtn];
    
    UIImageView *logoimg = [UIImageView new];
    [logoimg setImage:[UIImage imageNamed:@"welcomewineyune"]];
    [self.view addSubview:logoimg];
    
    [logoimg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.view);
        make.width.offset(171);
        make.height.offset(136);
        make.top.equalTo(self.view.mas_top).offset(NavigationContentTop+20);
    }];
    
    UILabel *titleLabel = [UILabel new];
    titleLabel.text = @"输入验证码";
    titleLabel.textColor = UIColorWhite;
    titleLabel.font = [UIFont boldPingFangSCOfSize:24];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:titleLabel];
    
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.view);
        make.top.equalTo(logoimg.mas_bottom).offset(30);
    }];
    
    self.phoneLabel = [UILabel new];
    self.phoneLabel.text = [NSString stringWithFormat:@"验证码已发送至 %@", self.phoneNumber ?: @""];
    self.phoneLabel.textColor = UIColorWhite;
    self.phoneLabel.font = [UIFont PingFangSCRegular:14];
    self.phoneLabel.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:self.phoneLabel];
    
    [self.phoneLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.view);
        make.top.equalTo(titleLabel.mas_bottom).offset(10);
    }];
    
    self.codeTF = [[QMUITextField alloc]init];
    self.codeTF.font = [UIFont PingFangSCRegular:17];
    self.codeTF.keyboardType = UIKeyboardTypeNumberPad;
    self.codeTF.placeholder = @"请输入验证码";
    self.codeTF.delegate = self;
    self.codeTF.maximumTextLength = 10;
    self.codeTF.textColor = UIColorWhite;
    self.codeTF.placeholderColor = UIColorWhite;
    self.codeTF.textAlignment = NSTextAlignmentCenter;
    [self.codeTF addTarget:self action:@selector(textFieldDidChange:) forControlEvents:UIControlEventEditingChanged];
    [self.view addSubview:self.codeTF];
    
    [self.codeTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view.mas_left).offset(33);
        make.right.equalTo(self.view.mas_right).offset(-33);
        make.height.offset(58);
        make.top.equalTo(self.phoneLabel.mas_bottom).offset(40);
    }];
    
    UIView *lineView = [[UIView alloc]init];
    lineView.backgroundColor = UIColorMakeWithHex(@"#EEEEEE");
    [self.view addSubview:lineView];
    
    [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(self.codeTF);
        make.top.equalTo(self.codeTF.mas_bottom);
        make.height.offset(1);
    }];
    
    self.getCodeBtn = [[UIButton alloc]init];
    [self.getCodeBtn.titleLabel setFont:[UIFont boldPingFangSCOfSize:15]];
    self.getCodeBtn.layer.masksToBounds = YES;
    self.getCodeBtn.layer.cornerRadius = 4;
    [self.getCodeBtn setTitle:@"重新获取" forState:UIControlStateNormal];
    [self.getCodeBtn addTarget:self action:@selector(getPhoneCode:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.getCodeBtn];
    
    [self.getCodeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.offset(31);
        make.top.equalTo(lineView.mas_bottom).offset(20);
        make.centerX.mas_equalTo(self.view);
        make.width.offset(95);
    }];
    
    // 开始倒计时
    [self startCountdown];
    
    self.loginButton=[[UIButton alloc]init];
    [self.loginButton setTitle:@"登录" forState:UIControlStateNormal];
    [self.loginButton.titleLabel setFont:[UIFont boldPingFangSCOfSize:18]];
    [self.loginButton setBackgroundColor:UIColorMakeWithHex(@"#E58E93")];
    [self.loginButton setTitleColor:UIColorWhite forState:UIControlStateNormal];
    [self.loginButton addTarget:self action:@selector(loginFunction) forControlEvents:UIControlEventTouchUpInside];
    self.loginButton.userInteractionEnabled = NO;
    self.loginButton.layer.cornerRadius = 26;
    [self.view addSubview:self.loginButton];
    
    [self.loginButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view.mas_left).offset(26);
        make.right.equalTo(self.view.mas_right).offset(-26);
        make.top.equalTo(self.getCodeBtn.mas_bottom).offset(40);
        make.height.offset(50);
    }];
}

- (void)startCountdown {
    self.codeNumber = 60;
    self.codeTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(cycleTimeOutAction:) userInfo:nil repeats:YES];
    [self codeAvailable:NO];
}

//倒计时
- (void)cycleTimeOutAction:(id)sender{
    [self.getCodeBtn setTitle:[NSString stringWithFormat:@"%lds",(long)self.codeNumber] forState:UIControlStateNormal];
    self.codeNumber = self.codeNumber - 1;
    if(self.codeNumber == -1){
        [self cancelphonecode];
    }
}

//重新获取验证码
- (void)cancelphonecode{
    [self.codeTimer invalidate];
    self.codeTimer = nil;
    [self codeAvailable:YES];
    [self.getCodeBtn setTitle:@"重新获取" forState:UIControlStateNormal];
    self.codeNumber = 60;
}

//验证码按钮是否可用
- (void)codeAvailable:(BOOL)isCode{
    if (isCode == YES) {
        self.getCodeBtn.userInteractionEnabled = YES;
        [self.getCodeBtn setTitleColor:[YDMyColors getMainRedColor] forState:UIControlStateNormal];
        [self.getCodeBtn setBackgroundColor:UIColorWhite];
    }else{
        self.getCodeBtn.userInteractionEnabled = NO;
        [self.getCodeBtn setTitleColor:UIColorWhite forState:UIControlStateNormal];
        [self.getCodeBtn setBackgroundColor:UIColorMakeWithHex(@"#E58E93")];
    }
}

#pragma mark  -监听uitextfield的值得变化
- (void)textFieldDidChange:(UITextField *)textField{
    [self istouchLoginBtn];
}

- (void)istouchLoginBtn{
    if (self.codeTF.text.length > 5) {
        self.loginButton.userInteractionEnabled = YES;
        [self.loginButton setBackgroundColor:UIColorWhite];
        [self.loginButton setTitleColor:[YDMyColors getMainRedColor] forState:UIControlStateNormal];
    }else{
        self.loginButton.userInteractionEnabled = NO;
        [self.loginButton setBackgroundColor:UIColorMakeWithHex(@"#E58E93")];
        [self.loginButton setTitleColor:UIColorWhite forState:UIControlStateNormal];
    }
}

//获取验证码
- (void)getPhoneCode:(UIButton *)sender{
    if (![HYTools checkTelNumber:self.phoneNumber]) {
        [HYTools showAlertMsg:@"手机号格式不正确！"];
        return;
    }

    WKWebViewConfiguration * configuration = [[WKWebViewConfiguration alloc] init];
    [configuration.userContentController addScriptMessageHandler:[[WeakScriptMessageDelegate alloc] initWithDelegate:self] name:@"getTickit"];
    self.webview = [[WKWebView alloc]initWithFrame:CGRectMake(0.0, 0, SCREEN_WIDTH, SCREEN_HEIGHT+30) configuration:configuration];
    self.webview.UIDelegate = self;
    self.webview.userInteractionEnabled = YES;
    self.webview.navigationDelegate = self;
    [self.view addSubview:self.webview];
    self.webview.backgroundColor = [UIColor clearColor];
    [self.webview setOpaque:NO];
    self.webview.scrollView.scrollEnabled = NO;
    [self.webview loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:jsloginUrl]]];
}

#pragma mark--登录
- (void)loginFunction{
    MJWeakSelf
    if([HYTools StringisEmpty:self.codeTF.text])
    {
        [HYTools showAlertMsg:@"请输入验证码"];
        return;
    }
    
    setUserAccount(self.phoneNumber);
    
    NSMutableDictionary *tempDic=[[NSMutableDictionary alloc]init];
    [tempDic setValue:[NSString stringWithFormat:@"%@",self.phoneNumber] forKey:@"telephone"];
    [tempDic setValue:[NSString stringWithFormat:@"%@",self.codeTF.text] forKey:@"code"];
    [tempDic setValue:@(2) forKey:@"logintype"];//登录类型 1：密码 2：短信
    [tempDic setValue:getClientId forKey:@"clientid"];
    [tempDic setValue:@"2" forKey:@"reg_from"];
    
    [QMUITips showLoadingInView:weakSelf.view];
    [[NetWorkTool sharedTools]requestMethods:POST URL:getComLoginUrl parameters:tempDic andBeingLoaded:NO andCache:NO success:^(id responseObject){
        NSString * securitycheckval = StringIsNull(responseObject[@"data"][@"token"]);
        setUserID(responseObject[@"data"][@"uid"]);
        [HYTools GeTuiSdkUnbindAlias];
        setToken(securitycheckval);
        DLog(@"---11111------%@------%@",responseObject[@"data"][@"uid"],securitycheckval);
        Synchron;
        [QMUITips hideAllTips];
        weakSelf.view.userInteractionEnabled = NO;
        UIWindow *window = [UIApplication sharedApplication].windows[0];
        [weakSelf getUserInfoData:^{
            [QMUITips showSucceed:@"登录成功" inView:window hideAfterDelay:1.5f];
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf returnToRoot];
            });
        }];
       
    } failure:^(NSError *error) {
//        [QMUITips hideAllTips];
    }];
}

#pragma mark - 登录请求
- (void)getUserInfoData:(void(^)(void))completion{
    // 这里可以添加获取用户信息的逻辑
    if (completion) {
        completion();
    }
}

- (void)returnToRoot{
    if (self.LoginSuccess) {
        self.LoginSuccess(self);
    } else {
        YDTabBarViewController *tabBar = [[YDTabBarViewController alloc]init];
        CATransition *transtition = [CATransition animation];
        transtition.duration = 0.5;
        transtition.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut];
        [UIApplication sharedApplication].windows[0].rootViewController = tabBar;
        [[UIApplication sharedApplication].windows[0].layer addAnimation:transtition forKey:@"animation"];
    }
}

#pragma mark - WKWebView Delegate Methods

// 页面开始加载时调用
- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation {
    [[UIApplication sharedApplication] setNetworkActivityIndicatorVisible:YES];
    [QMUITips showLoading:@"请稍后..." inView:self.view];
}

// 页面加载完成之后调用
- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    [[UIApplication sharedApplication] setNetworkActivityIndicatorVisible:NO];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [QMUITips hideAllTips];
    });
}

// 页面加载失败时调用
- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(null_unspecified WKNavigation *)navigation withError:(NSError *)error {
    [[UIApplication sharedApplication] setNetworkActivityIndicatorVisible:NO];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [QMUITips hideAllTips];
    });
}

#pragma mark WKNavigationDelegate
- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(nonnull WKNavigationAction *)navigationAction decisionHandler:(nonnull void (^)(WKNavigationActionPolicy))decisionHandler{
    NSURL *URL = navigationAction.request.URL;
    NSString * currentURL = [[URL absoluteString] stringByRemovingPercentEncoding];
    DLog(@"the currentURL is:%@ %ld",currentURL,navigationAction.navigationType);
    decisionHandler(WKNavigationActionPolicyAllow); // 必须实现 加载
    return;
}

- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    NSLog(@"name = %@, body = %@", message.name, message.body);
    MJWeakSelf
    if ([message.name caseInsensitiveCompare:@"getTickit"] == NSOrderedSame) {
        if (message.body) {
            NSData *jsonData = [message.body dataUsingEncoding:NSUTF8StringEncoding];
            NSError *err;
            NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                options:NSJSONReadingMutableContainers
                                                                  error:&err];
            if (!err) {
                [self.webview removeFromSuperview];
                [self.userContentController removeScriptMessageHandlerForName:@"getTickit"];
                if ([dic[@"status"] isEqualToString:@"success"]) {
                    //图形验证成功 发送短信验证码
                    NSMutableDictionary *tempDic=[[NSMutableDictionary alloc]init];
                    [tempDic setValue:weakSelf.phoneNumber forKey:@"telephone"];
                    [tempDic setValue:HMSTR(@"%@",dic[@"ticket"]) forKey:@"ticket"];
                    [tempDic setValue:HMSTR(@"%@",dic[@"randstr"]) forKey:@"randstr"];
                    [[NetWorkTool sharedTools]requestMethods:POST URL:sendRegCodeUrl parameters:tempDic andBeingLoaded:YES andCache:NO success:^(id responseObject) {
                        [weakSelf startCountdown];
                        [HYTools showAlertMsg:@"验证码已重新发送"];
                    } failure:^(NSError *error) {
                        [HYTools showAlertMsg:@"发送失败，请稍后重试"];
                    }];
                }
            }
        }
    }else if ([message.name isEqualToString:@"Consol_logger"]) {
        NSLog(@"message = %@",message.body);
    }
}

@end
