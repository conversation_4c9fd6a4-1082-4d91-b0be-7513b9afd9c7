//
//  BalanceInfoCollectionViewCell.m
//  WineTalk
//
//  Created by AI Assistant on 2024/12/19.
//

#import "BalanceInfoCollectionViewCell.h"

@implementation BalanceInfoCollectionViewCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.contentView.backgroundColor = [UIColor whiteColor];
//    self.contentView.layer.cornerRadius = 8;
//    self.contentView.layer.masksToBounds = YES;
    
    // 图标
//    self.iconImageView = [[UIImageView alloc] init];
//    self.iconImageView.contentMode = UIViewContentModeScaleAspectFit;
//    [self.contentView addSubview:self.iconImageView];
    
    // 标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = [UIFont boldPingFangSCOfSize:16];
    self.titleLabel.textColor = [YDMyColors getlightblack];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:self.titleLabel];
    
    // 数值
    self.valueLabel = [[UILabel alloc] init];
    self.valueLabel.font = [UIFont PingFangSCRegular:12];
    self.valueLabel.textColor = [YDMyColors getlightTextColor];
    self.valueLabel.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:self.valueLabel];
    self.lineView = [[UIView alloc] init];
    self.lineView .backgroundColor =  UIColorMakeWithHex(@"#D8D8D8");
    
    [self.contentView addSubview:self.lineView];
    // 布局约束
   
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(15);
        make.centerX.equalTo(self.contentView);
      
    }];
//    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.equalTo(self.contentView).offset(9);
//        make.right.equalTo(self.titleLabel.mas_left).offset(4);
//        make.width.height.equalTo(@40);
//    }];
    [self.valueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-15);
        make.centerX.equalTo(self.contentView);
    }];
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(35);
        make.width.mas_equalTo(1);
        make.right.mas_equalTo(self.contentView);
        make.centerY.mas_equalTo(self.contentView);
    }];
}

@end
