//
//  PersonCenterViewController.m
//  WineTalk
//
//  Created by HFY on 2021/7/2.
//

#import "PersonCenterViewController.h"
#import "PersonView.h"
#import "OrderCollectionViewCell.h"
#import "JHCollectionViewFlowLayout.h"
#import "CouponCollectionViewCell.h"
#import "BalanceInfoCollectionViewCell.h"

#import "PersonInfoViewController.h" //个人信息
#import "DailyCheckViewController.h"//签到
#import "MyOrderViewController.h"//全部订单
#import "AttentionAndFansViewController.h"//关注||粉丝
#import "MyCollectViewController.h"//我的收藏
#import "MyRecepitonViewController.h"//我的酒会
#import "ShoppingCartViewController.h"//购物车
#import "AddressManagementViewController.h"//收货地址
#import "MyExperienceVoucherViewController.h"//体验券
#import "CouponsViewController.h"//我的优惠券
#import "MyCouponViewController.h"//我的优惠券
#import "FineReceptionListViewController.h" //发布酒会
#import "applyCertificationViewController.h"//申请认证
#import "SystemSetupViewController.h" //系统设置
#import "CertificationDetailsViewController.h"//认证
#import "AfterSalesListViewController.h"//售后列表

#import "SignPopCustView.h" //签到
#import "SignSuccessfullyView.h"

#import <TFPopup/TFPopup.h>
#import <QYSDK/QYSDK.h>
#import "BillingHistoryViewController.h"
#import "WishListViewController.h"
#import "MineAuctionCollectionCell.h"
#import "PublishAuctionSelectTypeView.h"
#import "DraftBoxManager.h"
#import "MyPostedAuctionViewController.h"
#import "LotCancleLikePopView.h"
#import "AuctionAuthenticationViewController.h"
#import "WZMChat.h"
#import "secondConfigModel.h"
@interface PersonCenterViewController ()<UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout,JHCollectionViewDelegateFlowLayout>


@property (nonatomic, strong) PersonView *personView;

@property (nonatomic, strong) UICollectionView *collectionView;

@property (nonatomic, strong) UICollectionReusableView *headerView;

@property (nonatomic, strong) NSArray *orderArray;

@property (nonatomic, strong) SignPopCustView *signPopV;

@property (nonatomic, strong) UserInfo *currentInfo;

@property (nonatomic, strong) NSMutableArray *list_1;

@property (nonatomic, strong) NSMutableArray *list_2;

@property (nonatomic, strong) NSMutableArray *list_3;

@property (nonatomic, strong) NSArray *weekArray;//签到相关

@property (nonatomic, assign) NSInteger days;

@property (nonatomic, strong) NSDictionary *signData;

/// 拍卖数组
@property (nonatomic, strong) NSArray *auctionArray;

/// 信用值label
@property (nonatomic, strong) UILabel *creditValueLabel;

/// 选择拍卖发布弹窗
@property (nonatomic, strong) PublishAuctionSelectTypeView *chooseAuctionTypeView;

@property (nonatomic, strong) LotCancleLikePopView *authenticationPopView;

@property (nonatomic, assign) NSInteger allUnreadCount;
@property (nonatomic, strong) secondConfigModel *secondModel;
@end

@implementation PersonCenterViewController

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
    if ([HYTools changeUidIsNull]) {
        [self refreshMyData];
//        self.tabBarController
        _allUnreadCount = [[[QYSDK sharedSDK] conversationManager] allUnreadCount];
    } else {
        
        [self getBottomConfiguration:nil];
    }
   
}


BeginIgnoreClangWarning(-Wdeprecated-declarations)
- (void)viewDidLoad {
    [super viewDidLoad];
    NSDictionary *secondConfig = [USERDEFAULTS objectForKey:@"secondConfigData"];
   _secondModel = [secondConfigModel mj_objectWithKeyValues:secondConfig];
    
    self.view.backgroundColor = rgba(245, 245, 245, 1);
    
    self.orderArray = @[@{@"menuname":@"待支付",@"icon":@"orderdetails_zhifu"},@{@"menuname":@"待发货",@"icon":@"orderdetails_fahuo"},@{@"menuname":@"待收货",@"icon":@"orderdetails_shouhuo"},@{@"menuname":@"写酒评",@"icon":@"orderdetails_write_wine_reviews"},@{@"menuname":@"全部订单",@"icon":@"orderdetails_all_orders"}];
    _auctionArray = @[@{@"menuname":@"发布拍品",@"icon":@"mine_auction_post_auction",@"jumpLink":@""},@{@"menuname":@"我发布的",@"icon":@"mine_auction_my_posted",@"jumpLink":@""},@{@"menuname":@"我的参拍",@"icon":@"mine_auction_my_partake",@"jumpLink":@"/packageH/pages/auction-my-participation/auction-my-participation"},
    @{@"menuname":@"资金明细",@"icon":@"mine_auction_bond",@"jumpLink":@"/packageH/pages/auction-funds-list-new/auction-funds-list-new"},@{@"menuname":@"拍卖证书",@"icon":@"mine_auction_certificate",@"jumpLink":@"/packageH/pages/auction-certificate-list/auction-certificate-list?isFromMine=1"},@{@"menuname":@"我关注的",@"icon":@"mine_auction_remind",@"jumpLink":@"/packageH/pages/auction-remind/auction-remind?isFromMine=1"},];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(mePageChange:) name:ME_PAGE_DID_CHANGE_NOTIFICATION object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(upUser:) name:UPDATE_USER_NOTIFICATION object:nil];
   
    if (@available(iOS 11, *)) {
         self.collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
         self.automaticallyAdjustsScrollViewInsets = NO;
    }
    if (SafeTop == 20) {
        [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.mas_equalTo(self.view);
            make.bottom.equalTo(self.view.mas_bottom).offset(-62);
        }];
    }else{
        [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.mas_equalTo(self.view);
            make.bottom.equalTo(self.view.mas_bottom).offset(-(levelHome_Height-10));
        }];
    }
    
//    if ([HYTools changeUidIsNull]) {
//        [self refreshMyData];
//    } else {
//        [self getBottomConfiguration:nil];
//    }
    //    刷新
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshPersonCenterData) name: @"refreshPersonCenterData" object:nil];
    
//    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(updateBottomView:) name:@"updateBottomView" object:nil];
//    _ticketNewPeople = [[NewcomerCouponBottomView alloc] init];
//    if([getClickBottomDelete intValue]) {
//        _ticketNewPeople.hidden = YES;
//    } else {
////        [self updateBottomView:nil];
//    }
//    _ticketNewPeople.isLogin = ![HYTools changeUidIsNull];
//    [self.view addSubview:_ticketNewPeople];
//    CGFloat bottomHeight = 0;
//    if(SafeBottom) {
//        bottomHeight =TabBarHeight;
//    } else {
//        bottomHeight = 60;
//    }
//    [_ticketNewPeople mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.left.right.mas_equalTo(self.view);
//        make.bottom.equalTo(self.view.mas_bottom).offset(-bottomHeight);
//        make.height.mas_equalTo(SizeWidth(45));
//    }];
}
EndIgnoreClangWarning

- (PersonView *)personView{
    if (!_personView) {
        _personView = [[PersonView alloc]initWithFrame:CGRectMake(0, 0, ScreenWidth, SizeWidth(254))];
        _personView.target = self;
        _personView.tutouSelecter = @selector(tuTouClick:);
        
        _personView.listSelecter = @selector(tapGREvent:);
        [_personView.setBtn addTarget:self action:@selector(setBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [_personView.rzBtn addTarget:self action:@selector(rzBtnClick) forControlEvents:UIControlEventTouchUpInside];
        UITapGestureRecognizer *tapGR = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(headImgClick)];
        [_personView.headImg addGestureRecognizer:tapGR];
        UITapGestureRecognizer *tapGR1 = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(headImgClick)];
        [_personView.nikeLab addGestureRecognizer:tapGR1];
        if ([[UserInforObject userSingleton]userInfo].nickname.length != 0) {
            _personView.listArrs = [[NSMutableArray alloc]initWithObjects:@{@"title":@"收藏",@"num":@([[UserInforObject userSingleton]userInfo].collect_nums)},@{@"title":@"足迹",@"num":@([[UserInforObject userSingleton]userInfo].footprint_nums)}, @{@"title":@"关注",@"num":@([[UserInforObject userSingleton]userInfo].attention_nums)}, @{@"title":@"粉丝",@"num":@([[UserInforObject userSingleton]userInfo].fan_nums)}, nil];
        }else{
            _personView.listArrs = [[NSMutableArray alloc]initWithObjects:@{@"title":@"收藏",@"num":@"0"},@{@"title":@"足迹",@"num":@"0"}, @{@"title":@"关注",@"num":@"0"}, @{@"title":@"粉丝",@"num":@"0"}, nil];
        }
        MJWeakSelf
        [_personView.levelView addTapGesture:^(UIView *label) {
            if ([HYTools changeUidIsNull]) {
                GeneralWebViewController *activeWeb=[[GeneralWebViewController alloc]init];
                activeWeb.url = HMSTR(@"%@",myGradesUrl);
                activeWeb.isStatusBar = YES;
                [weakSelf.navigationController pushViewController:activeWeb animated:YES];
            }
        }];
        _personView.isNotNeedTutou = NO;
        _personView.backgroundColor = [UIColor yellowColor];
    }
    return _personView;
}
//双击刷新个人中心
- (void)refreshPersonCenterData {
    [self.jyheader beginRefreshing];
}

- (void)refreshMyData{
    MJWeakSelf
    [weakSelf getBottomConfiguration:nil];
//    [weakSelf getBottomConfiguration:nil];
    //创建队列
    dispatch_queue_t queue = dispatch_get_global_queue(0, 0);
    //创建队列组
    dispatch_group_t group = dispatch_group_create();
    
    //队列组异步
    dispatch_group_async(group, queue, ^{
         dispatch_group_enter(group);
         [weakSelf getUserInfoData:^() {
              dispatch_group_leave(group);
         }];
        
        dispatch_group_enter(group);
        [weakSelf getMyTask:^() {
             dispatch_group_leave(group);
        }];
    });
    
    //网络请求完成后加载视图
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        [weakSelf updateMyView];
    });
}

- (void)updateMyView {
    MJWeakSelf
    [UIView animateWithDuration:0.4 animations:^{
        if ([weakSelf.collectionView.mj_header isRefreshing]) {
            [weakSelf.collectionView.mj_header endRefreshing];
        }else if ([weakSelf.collectionView.mj_footer isRefreshing]){
            [weakSelf.collectionView.mj_footer endRefreshing];
        }
        
        [UIView performWithoutAnimation:^{
            weakSelf.personView.listArrs = [[NSMutableArray alloc]initWithObjects:@{@"title":@"收藏",@"num":@(weakSelf.currentInfo.collect_nums)},@{@"title":@"足迹",@"num":@(weakSelf.currentInfo.footprint_nums)}, @{@"title":@"关注",@"num":@(weakSelf.currentInfo.attention_nums)}, @{@"title":@"粉丝",@"num":@(weakSelf.currentInfo.fan_nums)}, nil];
            if([HYTools changeUidIsNull]){
                weakSelf.personView.levelView.hidden = NO;
                weakSelf.personView.rzBtn.hidden = NO;
                weakSelf.personView.nikeLab.hidden = NO;
                weakSelf.personView.loginLabel.hidden = YES;
                weakSelf.personView.nikeLab.text = weakSelf.currentInfo.nickname.length == 0 ? @"酒云网用户" : weakSelf.currentInfo.nickname;
                
                weakSelf.personView.levelView.userType = weakSelf.currentInfo.type;
                weakSelf.personView.levelView.leveNum = weakSelf.currentInfo.user_level.integerValue;
                if(weakSelf.currentInfo.certified_info.length) {
                    [weakSelf.personView.rzBtn setTitle:weakSelf.currentInfo.certified_info forState:UIControlStateNormal];
                    weakSelf.personView.setIconImageV.hidden = NO;
                } else {
                    [weakSelf.personView.rzBtn setTitle:@"申请认证" forState:UIControlStateNormal];
                    weakSelf.personView.setIconImageV.hidden = YES;
                }
               
                
            } else {
                weakSelf.personView.setIconImageV.hidden = YES;
                weakSelf.personView.nikeLab.hidden = YES;
                weakSelf.personView.loginLabel.hidden = NO;
                weakSelf.personView.levelView.hidden = YES;
                weakSelf.personView.rzBtn.hidden = YES;
//                [weakSelf.personView.rzBtn setTitle:weakSelf.currentInfo.certified_info forState:UIControlStateNormal];
            }
            
            if (weakSelf.tabBarController.selectedIndex == 4 && [HYTools changeUidIsNull]) {
                
               NSDictionary *dict = [USERDEFAULTS objectForKey:[NSString stringWithFormat:@"%@dontPromptSign",getUserID]];
                
                if(([[NSDate date] timeIntervalSince1970] - [dict[@"time"] doubleValue])/86400 > 7){
                    [weakSelf showSignView];
                } else {
                    if(![dict[@"status"] intValue]) {
                        [weakSelf showSignView];
                    }
                }
            }
           //刷新界面
            [weakSelf.collectionView reloadData];
         }];
         [QMUITips hideAllTipsInView:self.collectionView];
    }];
}

- (void)showSignView{
    MJWeakSelf
    if (!weakSelf.signPopV) {
        if ([weakSelf.signData[@"status"] integerValue] == 0) {
            weakSelf.signPopV = [[SignPopCustView alloc]initWithFrame:CGRectMake(0, 0, 253, 402)];
            [weakSelf.signPopV.SignInBtn addUpInside:^(UIButton *button) {
                
                [[NetWorkTool sharedTools]requestMethods:POST URL:sendSignInUrl parameters:nil andBeingLoaded:NO andCache:NO success:^(id responseObject) {
                    //本地保存签到状态
                    [weakSelf.signPopV tf_hide];
                    SignSuccessfullyView *v = [[SignSuccessfullyView alloc]initWithFrame:CGRectMake(0, 0, 253, 386)];
                    
                    [v.SignInBtn addUpInside:^(UIButton *button) {
                        DailyCheckViewController *DailyBtn = [DailyCheckViewController new];
                        [weakSelf.navigationController pushViewController:DailyBtn animated:YES];
                        [button.superview tf_hide];
                    }];
                    NSInteger rabbit = 0;
                    if (weakSelf.days <= weakSelf.weekArray.count) {
                        rabbit = [weakSelf.weekArray[weakSelf.days-1][@"rabbit"] integerValue];
                    }
                    if (rabbit == 0) {
                        rabbit = 1;
                    }
                    if (weakSelf.days == 7) {
                        NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"恭喜您，您已连续签到%ld天 获得额外+%ld兔头",weakSelf.days,rabbit]];
                        NSRange range1 = [[str string] rangeOfString:[NSString stringWithFormat:@"%ld",weakSelf.days]];
                        [str addAttribute:NSForegroundColorAttributeName value:UIColorMakeWithHex(@"#F8A233") range:range1];
                        [str addAttribute:NSFontAttributeName value:[UIFont PingFangSCRegular:16] range:range1];
                        NSRange range2 = [[str string] rangeOfString:[NSString stringWithFormat:@"+%ld",rabbit]];
                        [str addAttribute:NSForegroundColorAttributeName value:UIColorMakeWithHex(@"#F8A233") range:range2];
                        [str addAttribute:NSFontAttributeName value:[UIFont PingFangSCRegular:16] range:range2];
                        v.bomLabel.attributedText = str;
                        v.signLabel.text = @"满签成功";
                    }else{
                        NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"恭喜您，您已连续签到%ld天 获得+%ld兔头",weakSelf.days,rabbit]];
                        NSRange range1 = [[str string] rangeOfString:[NSString stringWithFormat:@"%ld",weakSelf.days]];
                        [str addAttribute:NSForegroundColorAttributeName value:UIColorMakeWithHex(@"#F8A233") range:range1];
                        [str addAttribute:NSFontAttributeName value:[UIFont PingFangSCRegular:16] range:range1];
                        NSRange range2 = [[str string] rangeOfString:[NSString stringWithFormat:@"+%ld",rabbit]];
                        [str addAttribute:NSForegroundColorAttributeName value:UIColorMakeWithHex(@"#F8A233") range:range2];
                        [str addAttribute:NSFontAttributeName value:[UIFont PingFangSCRegular:16] range:range2];
                        v.bomLabel.attributedText = str;
                        v.signLabel.text = @"签到成功";
                    }
                    TFPopupParam *param = [TFPopupParam new];
                    UIWindow *window = [UIApplication sharedApplication].windows[0];
                    [v tf_showScale:window offset:CGPointMake(0, 0) popupParam:param];
                    
                    [[NSNotificationCenter defaultCenter] postNotificationName:ME_PAGE_DID_CHANGE_NOTIFICATION object:nil userInfo:nil];
                } failure:^(NSError *error) {
                    [weakSelf.signPopV tf_hide];
                }];
            }];
            [weakSelf.signPopV.dontPromeptBtn addTapGesture:^(UIView *label) {
//                [weakSelf.signPopV tf_hide];
                weakSelf.signPopV.dontPromeptBtn.selected = !weakSelf.signPopV.dontPromeptBtn.selected;
                if(weakSelf.signPopV.dontPromeptBtn.selected) {
                    NSString *number = [NSString stringWithFormat:@"%.0f",[[NSDate date] timeIntervalSince1970]];
                    [USERDEFAULTS setObject:@{@"status":@(1),@"time":number} forKey:[NSString stringWithFormat:@"%@dontPromptSign",getUserID]];
                    [USERDEFAULTS synchronize];
                } else {
                    [USERDEFAULTS setObject:@{@"status":@(0),@"time":@"0"} forKey:[NSString stringWithFormat:@"%@dontPromptSign",getUserID]];
                    [USERDEFAULTS synchronize];
                }
                
               
            }];
            weakSelf.signPopV.toDaysign_status = [weakSelf.signData[@"status"] integerValue];
            weakSelf.signPopV.toDaysignDic = weakSelf.signData;
            weakSelf.signPopV.topLabel.text = HMSTR(@"%@",weakSelf.signData[@"signin_desc"]);
            weakSelf.signPopV.dataAry = [weakSelf.weekArray mutableCopy];
            
            TFPopupParam *param = [TFPopupParam new];
            UIWindow *window = [UIApplication sharedApplication].windows[0];
            [weakSelf.signPopV tf_showScale:window offset:CGPointMake(0, 0) popupParam:param];
        }
    }
}

#pragma mark---获取任务列表
- (void)getMyTask:(void(^)(void))callBack{
    MJWeakSelf
    [[NetWorkTool sharedTools]requestMethods:GET URL:getDilyTasksListUrl parameters:nil andBeingLoaded:NO andCache:NO success:^(id responseObject) {
        weakSelf.weekArray = responseObject[@"data"][@"signin"];
        weakSelf.days = [responseObject[@"data"][@"signin_record"][@"days"] integerValue];
        weakSelf.signData = responseObject[@"data"][@"signin_record"];
        if (callBack) {
             callBack();
        }
    } failure:^(NSError *error) {
        if (callBack) {
             callBack();
        }
    }];
}

#pragma mark --- 获取用户信息
//获取用户信息
- (void)getUserInfoData:(void(^)(void))callBack{
    MJWeakSelf
    [[NetWorkTool sharedTools]requestMethods:GET URL:getUserAllInfoUrl parameters:nil andBeingLoaded:NO andCache:NO success:^(id responseObject) {
        DLog(@"第三方:%@",getUserID);
        //用户信息
        NSDictionary *dic =[responseObject objectForKey:@"data"];
        weakSelf.currentInfo = [UserInfo mj_objectWithKeyValues:dic];
        [[UserInforObject userSingleton] saveUserInfo:weakSelf.currentInfo];
        setEnterGuide(@"0");
        setIsNewUser(dic[@"is_new_user"]);
        setCreatedDate(dic[@"created_date"]);
        NSArray *ary = dic[@"protocol"];
        if (ary.count > 0) {
            setIsCheckAgreement(ary[0]);
        }else{
            setIsCheckAgreement(@"0");
        }
        Synchron;
//        if(!getClickBottomDelete.intValue) {
//            [[NSNotificationCenter defaultCenter] postNotificationName:@"updateBottomView" object:nil];
//        }
//
        if (callBack) {
             callBack();
        }
    } failure:^(NSError *error) {
        if (callBack) {
             callBack();
        }
    }];
}

#pragma mark --- 获取底部图标数据
- (void)getBottomConfiguration:(void(^)(void))callBack{
    NSMutableDictionary *tempDic = [[NSMutableDictionary alloc]init];
    [tempDic setValue:@(0) forKey:@"client"];
    [tempDic setValue:@(5) forKey:@"channel"];//频道 0 首页 1 闪购 2 现货速发 5个人中心
    MJWeakSelf
    [[NetWorkTool sharedTools]requestMethods:GET URL:getMarketingConfListUrl parameters:tempDic andBeingLoaded:NO andCache:YES success:^(id responseObject) {
        DLog(@"第三方:%@",getUserID);
        //用户信息
        NSDictionary *dic =[responseObject objectForKey:@"data"];
        NSArray *listAry = dic[@"list"];
        if (listAry.count > 0) {
            
            weakSelf.list_1 = [[NSMutableArray alloc] init];
            weakSelf.list_2 = [[NSMutableArray alloc] init];
            weakSelf.list_3 = [[NSMutableArray alloc] init];
            for (int i = 0; i < listAry.count; i++) {
                if([listAry[i][@"page_area"] intValue] == 1) {
                    [weakSelf.list_1 addObject:listAry[i]];
                } else if ([listAry[i][@"page_area"] intValue] == 2) {
                    [weakSelf.list_2 addObject:listAry[i]];
                } else if ([listAry[i][@"page_area"] intValue] == 3) {
                    [weakSelf.list_3 addObject:listAry[i]];
                }
            }
        }
        if(![HYTools changeUidIsNull]){
            weakSelf.currentInfo = [[UserInfo alloc] init];
            [weakSelf updateMyView];
        } else {
            [weakSelf.collectionView reloadData];
        }
        
        if (callBack) {
             callBack();
        }
    } failure:^(NSError *error) {
        if (callBack) {
             callBack();
        }
        if(![HYTools changeUidIsNull]){
            weakSelf.currentInfo = [[UserInfo alloc] init];
            [weakSelf updateMyView];
        }
    }];
}

- (UICollectionView *)collectionView{
    if (!_collectionView) {
        JHCollectionViewFlowLayout *flowLayout = [[JHCollectionViewFlowLayout alloc]init];
        _collectionView = [[UICollectionView alloc]initWithFrame:self.view.bounds collectionViewLayout:flowLayout];
        [self.view addSubview:_collectionView];
        _collectionView.backgroundColor = [UIColor clearColor];
        _collectionView.delegate  = self;
        _collectionView.dataSource = self;
        [_collectionView registerClass:[UICollectionViewCell class] forCellWithReuseIdentifier:@"userCell"];
        [_collectionView registerClass:[UICollectionViewCell class] forCellWithReuseIdentifier:@"cell"];
        [_collectionView registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"header"];
        [_collectionView registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:@"footer"];
        [_collectionView registerNib:[UINib nibWithNibName:@"OrderCollectionViewCell" bundle:nil] forCellWithReuseIdentifier:@"orderCell"];
        [_collectionView registerNib:[UINib nibWithNibName:@"OrderCollectionViewCell" bundle:nil] forCellWithReuseIdentifier:@"otherCell"];
        [_collectionView registerClass:[CouponCollectionViewCell class] forCellWithReuseIdentifier:@"tuTouCell"];
        [_collectionView registerClass:[BalanceInfoCollectionViewCell class] forCellWithReuseIdentifier:@"balanceInfoCell"];
        [_collectionView registerNib:[UINib nibWithNibName:@"MineAuctionCollectionCell" bundle:nil] forCellWithReuseIdentifier:@"MineAuctionCollectionCell"];
       
        _collectionView.showsVerticalScrollIndicator = NO;
        
        UIView *bgView = [[UIView alloc] initWithFrame:CGRectOffset(CGRectMake(0, 10, SCREEN_WIDTH, SCREEN_HEIGHT), 0, -self.view.bounds.size.height)];
        bgView.backgroundColor = UIColorMakeWithHex(@"#D50A0A");
        [_collectionView insertSubview:bgView atIndex:0];
        self.headerLabelColor = UIColorWhite;
        MJWeakSelf
        [self refreshHeaderWithAction:_collectionView AndBackBgColor:nil WithHandler:^(BOOL isRefreshHeader) {
            if ([HYTools changeUidIsNull]) {
                [weakSelf refreshMyData];
            } else {
                [weakSelf getBottomConfiguration:nil];
            }
            
        }];
//        _collectionView.delaysContentTouches = NO;
        [_collectionView sendSubviewToBack:bgView];
    }
    return _collectionView;
}
#pragma mark - 点击事件
- (void)headImgClick{
    if(![HYTools changeUidIsNull]){
        [[CommonFunc sharedInstance] judgeUnLogin:self message:@"你还没有登录,请先登录"];
        return;
    }
    PersonInfoViewController *vc = [PersonInfoViewController new];
    [self.navigationController pushViewController:vc animated:YES];
}
- (void)setBtnClick{
  
    
    if(![HYTools changeUidIsNull]){
        [[CommonFunc sharedInstance] judgeUnLogin:self message:@"你还没有登录,请先登录"];
        return;
    }

    SystemSetupViewController *systemVC = [[SystemSetupViewController alloc]init];
    [self.navigationController pushViewController:systemVC animated:YES];
   
}
- (void)rzBtnClick{
    if(![HYTools changeUidIsNull]){
        [[CommonFunc sharedInstance] judgeUnLogin:self message:@"你还没有登录,请先登录"];
        return;
    }
    if ([[UserInforObject userSingleton]userInfo].certified_info.length == 0) {
        applyCertificationViewController *systemVC = [[applyCertificationViewController alloc]init];
        [self.navigationController pushViewController:systemVC animated:YES];
    }else{
        CertificationDetailsViewController *vc = [[CertificationDetailsViewController alloc]init];
        [self.navigationController pushViewController:vc animated:YES];
    }
}

- (void)tapGREvent:(UITapGestureRecognizer *)sender{
    if(![HYTools changeUidIsNull]){
        [[CommonFunc sharedInstance] judgeUnLogin:self message:@"你还没有登录,请先登录"];
        return;
    }
    UIView *view = sender.view;
    if (view.tag==1) {
        //收藏
        MyCollectViewController *myCollectionVC = [[MyCollectViewController alloc]init];
        [self.navigationController pushViewController:myCollectionVC animated:YES];
    }
    if (view.tag==2) {
        GeneralWebViewController *activeWeb=[[GeneralWebViewController alloc]init];
        activeWeb.url = @"/packageE/pages/my-footprint/my-footprint";
        activeWeb.isHideNav = YES;
        [self.navigationController pushViewController:activeWeb animated:YES];
    }
    if (view.tag==3) {
        AttentionAndFansViewController *attentionVc = [[AttentionAndFansViewController alloc]init];
        attentionVc.type = 1;
        [self.navigationController pushViewController:attentionVc animated:YES];
        //关注
    }
    if (view.tag==4) {
        //粉丝
        AttentionAndFansViewController *attentionVc = [[AttentionAndFansViewController alloc]init];
        attentionVc.type = 2;
        [self.navigationController pushViewController:attentionVc animated:YES];
    }
}
- (void)tuTouClick:(UITapGestureRecognizer *)sender{
    if(![HYTools changeUidIsNull]){
        [[CommonFunc sharedInstance] judgeUnLogin:self message:@"你还没有登录,请先登录"];
        return;
    }
    NSLog(@"点击兔头%ld",sender.view.tag);
    if (sender.view.tag==1) {
        //兔头商店
        [CommonFunc buriedPointRequtstWithGenre:3 channel:7 region_id:401000 button_id:1 mid:@""];
        GeneralWebViewController *activeWeb=[[GeneralWebViewController alloc]init];
        activeWeb.url = rabbitHeadShopUrl;
        activeWeb.isHideNav = YES;
        activeWeb.isStatusBar = YES;
        [self.navigationController pushViewController:activeWeb animated:YES];
       
      
    }
    if (sender.view.tag==2) {
       
        //大转盘
        [CommonFunc buriedPointRequtstWithGenre:3 channel:7 region_id:401000 button_id:2 mid:@""];
        GeneralWebViewController *activeWeb=[[GeneralWebViewController alloc]init];
        activeWeb.url = rabbitTurntableUrl;
        activeWeb.isHideNav = YES;
        activeWeb.isStatusBar = NO;
        [self.navigationController pushViewController:activeWeb animated:YES];
    }
    if (sender.view.tag==3) {
        //签到
        [CommonFunc buriedPointRequtstWithGenre:3 channel:7 region_id:401000 button_id:3 mid:@""];
        DailyCheckViewController *DailyBtn = [DailyCheckViewController new];
        [self.navigationController pushViewController:DailyBtn animated:YES];
    }
}
- (void)moreOrderBtn{
    if(![HYTools changeUidIsNull]){
        [[CommonFunc sharedInstance] judgeUnLogin:self message:@"你还没有登录,请先登录"];
        return;
    }
    MyOrderViewController *orderVc = [MyOrderViewController new];
    [self.navigationController pushViewController:orderVc animated:YES];

}
#pragma mark - JHCollectionViewDelegateFlowLayout
- (UIColor *)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout backgroundColorForSection:(NSInteger)section
{
    if (section == 2 || section == 0) {
        return [UIColor clearColor];
    }
    return [UIColor whiteColor];
}
#pragma mark -- UICollectionViewDelegateFlowLayout

//每个collectionView的大小
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    
    if (indexPath.section == 0) {
        return (CGSize){collectionView.frame.size.width , SizeWidth(261)};
    }
    if (indexPath.section==1 || indexPath.section== 3) {
        return (CGSize){(collectionView.frame.size.width-24)/5 ,75};
    }
    if (indexPath.section==2) {
        if(_secondModel.show_balance){
            return (CGSize){(collectionView.frame.size.width-24)/3  ,75};
        }
        return (CGSize){(collectionView.frame.size.width-24)/2-5  ,75};
      
    }
//    if (indexPath.section==4) {
//        return (CGSize){100  ,45};
//    }
    return (CGSize){(collectionView.frame.size.width-24)/5 ,76};
}
//定义每个UICollectionView 的 margin
- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section{
    if (section == 0) {
        return UIEdgeInsetsMake(0, 0, 0, 0);
    }
    if (section == 1 ) {
        return UIEdgeInsetsMake(0, 12, 0, 12);
    }
   
    if (section == 3) {
        return UIEdgeInsetsMake(0, 12, 0, 12);
    }
    
    return UIEdgeInsetsMake(0, 12, 0, 12);
}
//设置每个item水平间距
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section
{
//    if(section == 4) {
//        return SizeWidth(25);
//    }
    return 0;
}
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return 0;
}

#pragma mark -UICollectionViewDataSource
//定义展示的UICollectionViewCell的个数
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    if (section==0) {
        return 1;
    }
    if (section==1) {
        return _orderArray.count;
    }
    if (section==3) {
        return self.list_1.count;
    }
    if (section == 2) {
        if(_secondModel.show_balance) {
            return 3;
        }
        return 2;
    }
   
    if(section == 4) {
        return self.list_2.count;
    }
    return self.list_3.count;
}
//展示的section个数
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return 6;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    if (indexPath.section==0) {
        
        UICollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"userCell" forIndexPath:indexPath];
        [cell.contentView addSubview:self.personView];
        [_personView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_offset(0);
            make.left.right.mas_equalTo(cell.contentView);
            make.bottom.mas_equalTo(cell.contentView);
        }];
        cell.backgroundColor = [UIColor clearColor];
        
        [self.personView.headImg sd_setImageWithURL:[NSURL URLWithString:[[UserInforObject userSingleton]userInfo].avatar_image] placeholderImage:[YDMyImagePath defultIconImage]];
        return cell;
    }
    if (indexPath.section==1) {
        OrderCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"orderCell" forIndexPath:indexPath];
        NSDictionary *dic = self.orderArray[indexPath.row];
        [cell.iconImageView setImage:[UIImage imageNamed:dic[@"icon"]]];
        cell.nameLab.text = dic[@"menuname"];
       
        cell.iconImageView.qmui_badgeOffset = CGPointMake(-5,7);
        cell.iconImageView.qmui_badgeInteger = 0;
        cell.imageHeight.constant = 20;
        cell.imageWidth.constant  = 24;
        if (indexPath.row == 0 && self.currentInfo.unpaid_nums != 0) { //待支付
            cell.iconImageView.qmui_badgeInteger =  [self handleNumberWithAfferent:self.currentInfo.unpaid_nums];
        }
        
        if (indexPath.row == 1 && self.currentInfo.paid_nums != 0){ //待发货
            cell.iconImageView.qmui_badgeInteger = [self handleNumberWithAfferent:self.currentInfo.paid_nums];
        }
        if (indexPath.row == 2 && self.currentInfo.shipped_nums != 0){ //待收货
            cell.iconImageView.qmui_badgeInteger = [self handleNumberWithAfferent:self.currentInfo.shipped_nums];
        }
        if (indexPath.row == 3 && self.currentInfo.wine_comment_nums.intValue != 0){ //酒评论
            cell.iconImageView.qmui_badgeInteger = [self handleNumberWithAfferent:self.currentInfo.wine_comment_nums.integerValue];
        }

        return cell;
    }
    if (indexPath.section==2) {
        if(_secondModel.show_balance) {
            BalanceInfoCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"balanceInfoCell" forIndexPath:indexPath];

            if (indexPath.row == 0) {
                // 余额
                cell.lineView.hidden = NO;
                
                cell.valueLabel.text = @"余额";
               
                NSString*balanceStr = [NSString stringWithFormat:@"¥%.2f", self.currentInfo.recharge_balance + self.currentInfo.bonus_balance];
                NSMutableAttributedString *noteStr = [[NSMutableAttributedString alloc] initWithString:balanceStr];
                // 需要改变的区间(第一个参数，从第几位起，长度)
                
                // 改变字体大小及类型
                [noteStr addAttribute:NSFontAttributeName value:[UIFont boldPingFangSCOfSize:12] range:NSMakeRange(0, 1)];
                cell.titleLabel.attributedText =noteStr;
            } else if (indexPath.row == 1) {
                // 兔头
                cell.lineView.hidden = NO;
               
                cell.valueLabel.text = @"兔头";
                cell.titleLabel.text = [NSString stringWithFormat:@"%ld", self.currentInfo.rabbit];
            } else if (indexPath.row == 2) {
                // 优惠券
                cell.lineView.hidden = YES;
               
                cell.valueLabel.text = @"优惠券";
                NSString *str = [NSString stringWithFormat:@"%ld张", self.currentInfo.coupon_totals];
                
                // 创建Attributed
                NSMutableAttributedString *noteStr = [[NSMutableAttributedString alloc] initWithString:str];
                // 需要改变的区间(第一个参数，从第几位起，长度)
                NSRange range =  [str rangeOfString:[HYTools getDealNumwithstring:[NSString stringWithFormat:@"%ld",self.currentInfo.rabbit]]];
                // 改变字体大小及类型
                [noteStr addAttribute:NSFontAttributeName value:[UIFont boldPingFangSCOfSize:12] range:NSMakeRange(str.length-1, 1)];
                cell.titleLabel.attributedText = noteStr;
            }

            return cell;
        } else {
            CouponCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"tuTouCell" forIndexPath:indexPath];
                   if (!cell) {
                       cell = [[CouponCollectionViewCell alloc]initWithFrame:CGRectZero];
                   }
                   UIImageView *imgV = [[UIImageView alloc]initWithFrame:cell.bounds];
                   if (indexPath.row%2==0) {
                       imgV.image = [UIImage imageNamed:@"orange_bg"];
                       NSString *str = [NSString stringWithFormat:@"%@ 当前兔头",[HYTools getDealNumwithstring:[NSString stringWithFormat:@"%ld",self.currentInfo.rabbit]]];
                       
                       // 创建Attributed
                       NSMutableAttributedString *noteStr = [[NSMutableAttributedString alloc] initWithString:str];
                       // 需要改变的区间(第一个参数，从第几位起，长度)
                       NSRange range =  [str rangeOfString:[HYTools getDealNumwithstring:[NSString stringWithFormat:@"%ld",self.currentInfo.rabbit]]];
                       // 改变字体大小及类型
                       [noteStr addAttribute:NSFontAttributeName value:[UIFont boldPingFangSCOfSize:18] range:range];
                       // 为label添加Attributed
                       [cell.titleLab setAttributedText:noteStr];
                       
                       //----------------------------------------------------------------------------------------------------------------------------------------------------------------
                       NSMutableAttributedString *attri = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%ld兔头未领取 ",self.currentInfo.rabbit_available]];
                       
                       NSTextAttachment *attch = [[NSTextAttachment alloc] init];
                       // 表情图片
                       attch.image = [UIImage imageNamed:@"orange_arrow_icon"];
                       
                       // 设置图片大小
                       
                       attch.bounds = CGRectMake(0, 0, 4, 8);
                       // 创建带有图片的富文本
                       NSAttributedString *string = [NSAttributedString attributedStringWithAttachment:attch];
                       [attri appendAttributedString:string]; //在文字后面添加图片
                       // 用label的attributedText属性来使用富文本
                       cell.bottomLab.attributedText = attri;
                       cell.bottomLab.textColor = UIColorMakeWithHex(@"#F15D22");
                   }else{
                       imgV.image = [UIImage imageNamed:@"pink_bg"];
                       NSString *str = [NSString stringWithFormat:@"%ld 张/优惠券",self.currentInfo.coupon_totals];
                       // 创建Attributed
                       NSMutableAttributedString *noteStr = [[NSMutableAttributedString alloc] initWithString:str];
                       // 需要改变的区间(第一个参数，从第几位起，长度)
                       NSRange range =  [str rangeOfString:[NSString stringWithFormat:@"%ld",self.currentInfo.coupon_totals]];
                       // 改变字体大小及类型
                       [noteStr addAttribute:NSFontAttributeName value:[UIFont boldPingFangSCOfSize:18] range:range];
                       // 为label添加Attributed
                       [cell.titleLab setAttributedText:noteStr];
                       //----------------------------------------------------------------------------------------------------------------------------------------------------------------
                       NSMutableAttributedString *attri = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%ld张即将过期 ",self.currentInfo.conpon_expirings]];
                       
                       NSTextAttachment *attch = [[NSTextAttachment alloc] init];
                       // 表情图片
                       attch.image = [UIImage imageNamed:@"arrow_red_icon"];
                       
                       // 设置图片大小
                       
                       attch.bounds = CGRectMake(0, 0, 4, 8);
                       // 创建带有图片的富文本
                       NSAttributedString *string = [NSAttributedString attributedStringWithAttachment:attch];
                       [attri appendAttributedString:string]; //在文字后面添加图片
                       // 用label的attributedText属性来使用富文本
                       cell.bottomLab.attributedText = attri;
                       cell.bottomLab.textColor = UIColorMakeWithHex(@"#E81710");
                   }
                   cell.backgroundView = imgV;
                   return cell;
        }
       
    }
//    if (indexPath.section==4) {
//        MineAuctionCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"MineAuctionCollectionCell" forIndexPath:indexPath];
////        cell.backgroundColor = [UIColor redColor];
//        NSDictionary *dic = self.auctionArray[indexPath.row];
//        [cell.iconImageView setImage:[UIImage imageNamed:dic[@"icon"]]];
//        cell.nameLabel.text = dic[@"menuname"];
//        cell.iconImageView.qmui_badgeInteger = 0;
//        cell.iconImageView.qmui_badgeOffset = CGPointMake(-12, 9);
//        if (indexPath.row == 5 && self.currentInfo.auction_reminder_nums.intValue != 0) {
//            cell.iconImageView.qmui_badgeInteger = self.currentInfo.auction_reminder_nums.intValue ;
//        }
//
//        return cell;
//    }
     OrderCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"otherCell" forIndexPath:indexPath];
    cell.allUnreadCount = _allUnreadCount;
    if (indexPath.section == 3) {
        cell.imageHeight.constant = 22;
        cell.imageWidth.constant  = 22;
        cell.dataDic = self.list_1[indexPath.row];
        cell.iconImageView.qmui_badgeOffset = CGPointMake(-5,10);
    }else if(indexPath.section == 4){
        cell.imageHeight.constant = 37;
        cell.imageWidth.constant  = 37;
        cell.iconImageView.qmui_badgeOffset = CGPointMake(-7,15);
        cell.dataDic = self.list_2[indexPath.row];
    } else if(indexPath.section == 5){
        cell.imageHeight.constant = 37;
        cell.imageWidth.constant  = 37;
        cell.iconImageView.qmui_badgeOffset = CGPointMake(-7,15);
        cell.dataDic = self.list_3[indexPath.row];
    }
    cell.iconImageView.qmui_badgeFont = [UIFont PingFangSCRegular:10];

    return cell;
}
- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath{
    if (indexPath.section == 1 ) {
        NSInteger count = [_collectionView numberOfItemsInSection:indexPath.section];
        UIBezierPath *maskPath = nil;
        CGSize size;
        if(indexPath.section == 1) {
            size = CGSizeMake(5, 5);
        } else {
            size = CGSizeMake(8, 8);
        }
        
        cell.layer.masksToBounds = YES;
        CGRect bound = cell.bounds ;
        if(indexPath.row % 2 == 0) {
            maskPath = [UIBezierPath bezierPathWithRoundedRect:bound
                                             byRoundingCorners:UIRectCornerBottomLeft
                                                   cornerRadii:size];
            
            CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
            //设置大小
            maskLayer.frame = bound;
            //设置图形样子
            maskLayer.path = maskPath.CGPath;
            cell.layer.mask = maskLayer;
        }
        if (indexPath.row == count - 1) {
            maskPath = [UIBezierPath bezierPathWithRoundedRect:bound
                                             byRoundingCorners: UIRectCornerBottomRight
                                                   cornerRadii:size];
            
            CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
            //设置大小
            maskLayer.frame = bound;
            //设置图形样子
            maskLayer.path = maskPath.CGPath;
            cell.layer.mask = maskLayer;
        }
        
        
    }else if (indexPath.section == 2) {
        if(_secondModel.show_balance){
            UIBezierPath *maskPath = nil;
            CGSize size = CGSizeMake(5, 5);
            cell.layer.masksToBounds = YES;
            CGRect bound = cell.bounds ;
            if (indexPath.row == 0) {
                maskPath = [UIBezierPath bezierPathWithRoundedRect:bound
                                                 byRoundingCorners: UIRectCornerTopLeft |UIRectCornerBottomLeft
                                                       cornerRadii:size];
                
                CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
                //设置大小
                maskLayer.frame = bound;
                //设置图形样子
                maskLayer.path = maskPath.CGPath;
                cell.layer.mask = maskLayer;
            }
            if (indexPath.row == 2) {
                maskPath = [UIBezierPath bezierPathWithRoundedRect:bound
                                                 byRoundingCorners: UIRectCornerBottomRight |UIRectCornerTopRight
                                                       cornerRadii:size];
                
                CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
                //设置大小
                maskLayer.frame = bound;
                //设置图形样子
                maskLayer.path = maskPath.CGPath;
                cell.layer.mask = maskLayer;
            }
        } else {
            UIBezierPath *maskPath = nil;
            CGSize size = CGSizeMake(5, 5);
            cell.layer.masksToBounds = YES;
            CGRect bound = cell.bounds ;
            
            maskPath = [UIBezierPath bezierPathWithRoundedRect:bound
                                             byRoundingCorners:UIRectCornerAllCorners
                                                   cornerRadii:size];
            
            CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
            //设置大小
            maskLayer.frame = bound;
            //设置图形样子
            maskLayer.path = maskPath.CGPath;
            cell.layer.mask = maskLayer;
        }
        
       
    }
    else if (indexPath.section == 4 || indexPath.section == 5) {
        UIBezierPath *maskPath = nil;
        CGSize size = CGSizeMake(5, 5);
        cell.layer.masksToBounds = YES;
        CGRect bound = cell.bounds ;
        
        maskPath = [UIBezierPath bezierPathWithRoundedRect:bound
                                         byRoundingCorners:UIRectCornerBottomRight | UIRectCornerBottomLeft
                                               cornerRadii:size];
        
        CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
        //设置大小
        maskLayer.frame = bound;
        //设置图形样子
        maskLayer.path = maskPath.CGPath;
        cell.layer.mask = maskLayer;
    }
    else  {
//        NSInteger count = [_collectionView numberOfItemsInSection:indexPath.section];
        UIBezierPath *maskPath = nil;
        CGSize size = CGSizeMake(5, 5);
        cell.layer.masksToBounds = YES;
        CGRect bound = cell.bounds ;
        
        maskPath = [UIBezierPath bezierPathWithRoundedRect:bound
                                         byRoundingCorners:UIRectCornerAllCorners
                                               cornerRadii:size];
        
        CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
        //设置大小
        maskLayer.frame = bound;
        //设置图形样子
        maskLayer.path = maskPath.CGPath;
        cell.layer.mask = maskLayer;
    }
    
}
//footer 高度
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout referenceSizeForFooterInSection:(NSInteger)section{
    if(section == 0) {
        return  (CGSize){collectionView.frame.size.width,0};
    } else if (section == 5) {
        return (CGSize){collectionView.frame.size.width,40};
    }
    return (CGSize){collectionView.frame.size.width,10};
}
//header 高度
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout referenceSizeForHeaderInSection:(NSInteger)section{
    if (section==1) {
        return CGSizeMake(collectionView.frame.size.width, 16);
    }  else if (section==4 ) {
        if(_list_2.count) {
            return CGSizeMake(collectionView.frame.size.width, 14);
        }
        return CGSizeMake(0, 0);
       
    } else if (section==5 ) {
        if(_list_3.count) {
            return CGSizeMake(collectionView.frame.size.width, 14);
        }
        return CGSizeMake(0, 0);
       
    }
    return CGSizeMake(0,0);
}
//header
- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath{
    
    if ([kind isEqualToString:UICollectionElementKindSectionHeader]) {
        _headerView = [collectionView dequeueReusableSupplementaryViewOfKind:kind
                                                         withReuseIdentifier:@"header"
                                                                forIndexPath:indexPath];
        if (indexPath.section == 1) {
            _headerView.backgroundColor = [UIColor clearColor];
            for(UIView*v in _headerView.subviews) {
                [v removeFromSuperview];
            }
            UIView *backView = [[UIView alloc]initWithFrame:CGRectMake(12, 0, _headerView.frame.size.width-24, _headerView.frame.size.height)];
           
            [_headerView addSubview:backView];
            backView.backgroundColor = [UIColor whiteColor];

            UIView *lineView = [UIView new];
            [backView addSubview:lineView];
            lineView.backgroundColor = rgba(238, 238, 238, 1);
            [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.height.mas_offset(1);
                make.left.mas_offset(12);
                make.bottom.mas_equalTo(backView.mas_bottom).offset(-1);
                make.right.mas_equalTo(backView.mas_right).offset(-12);
            }];
        }
//        else if (indexPath.section == 4) {
//            _headerView.backgroundColor = [UIColor clearColor];
//            for(UIView*v in _headerView.subviews) {
//                [v removeFromSuperview];
//            }
//
//            UIView *backView = [[UIView alloc]initWithFrame:CGRectMake(12, 0, _headerView.frame.size.width-24, _headerView.frame.size.height)];
//            [_headerView addSubview:backView];
//            backView.backgroundColor = [UIColor whiteColor];
//            UIBezierPath *maskPath = nil;
//            CGSize size = CGSizeMake(8, 8);
//            backView.layer.masksToBounds = YES;
//            CGRect bound = backView.bounds ;
//
//            maskPath = [UIBezierPath bezierPathWithRoundedRect:bound
//                                             byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight
//                                                   cornerRadii:size];
//
//            CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
//            //设置大小
//            maskLayer.frame = bound;
//            //设置图形样子
//            maskLayer.path = maskPath.CGPath;
//            backView.layer.mask = maskLayer;
//
//            UILabel *nameLab = [UILabel new];
//            [backView addSubview:nameLab];
//            [nameLab mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.height.mas_equalTo(22);
//                make.left.mas_offset(12);
//                make.top.mas_equalTo(backView).offset(10);
//            }];
//            nameLab.text = @"拍卖";
//            nameLab.font = [UIFont PingFangSemiboldofSize:16];
//            nameLab.textColor = [YDMyColors getlightblack];
//            UIStackView *creditValueView = [UIStackView new];
//            creditValueView.axis = UILayoutConstraintAxisHorizontal;
//            creditValueView.alignment = UIStackViewAlignmentCenter;
//            creditValueView.distribution = UIStackViewDistributionEqualSpacing;
//            creditValueView.spacing = 1;
//
//            creditValueView.backgroundColor = UIColorMakeWithHex(@"#FFF1D4");
//            creditValueView.userInteractionEnabled = YES;
//            MJWeakSelf
//            [creditValueView addTapGesture:^(UIView *label) {
//                if(![HYTools changeUidIsNull]){
//                    [[CommonFunc sharedInstance] judgeUnLogin:weakSelf message:@"你还没有登录,请先登录"];
//                    return;
//                }
//                GeneralWebViewController *activeWeb=[[GeneralWebViewController alloc]init];
//                activeWeb.url = @"/packageH/pages/auction-credit-values/auction-credit-values?isFromMine=1";
//                activeWeb.isHideNav = YES;
//                activeWeb.isStatusBar = YES;
//                [weakSelf.navigationController pushViewController:activeWeb animated:YES];
//            }];
//            [backView addSubview:creditValueView];
//            creditValueView.layer.cornerRadius = 10;
//            creditValueView.clipsToBounds = YES;
//            [creditValueView mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.centerY.mas_equalTo(nameLab);
//                make.right.mas_equalTo(backView.mas_right).offset(-12);
////                make.width.mas_equalTo(65);
//                make.height.mas_equalTo(20);
//            }];
//
//            UIView *spaceView1 = [UIView new];
//
//            [creditValueView addArrangedSubview:spaceView1];
//            [spaceView1 mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.width.mas_equalTo(6);
//                make.height.mas_equalTo(20);
//            }];
//
//            _creditValueLabel = [UILabel new];
//            _creditValueLabel.font = [UIFont boldPingFangSCOfSize:9];
//            _creditValueLabel.textColor = UIColorMakeWithHex(@"#9E632A");
//            [creditValueView addArrangedSubview:_creditValueLabel];
//            if(![HYTools changeUidIsNull]){
//                _creditValueLabel.text = @"100";
//            } else {
//                _creditValueLabel.text = _currentInfo.auction_credit_score;
//            }
//
//            UILabel *textLabel = [UILabel new];
//            textLabel.font = [UIFont PingFangSCRegular:9];
//            textLabel.text = @"信用值";
//            textLabel.textColor = UIColorMakeWithHex(@"#B68757");
//            [creditValueView addArrangedSubview:textLabel];
//
//
//
//
//            UIImageView *rightArrowImageView = [UIImageView new];
//            rightArrowImageView.image = [UIImage imageNamed:@"mine_auction_right_arrow"];
//            [creditValueView addArrangedSubview:rightArrowImageView];
//            UIView *spaceView = [UIView new];
//            [creditValueView addArrangedSubview:spaceView];
//            [spaceView mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.width.mas_equalTo(5);
//                make.height.mas_equalTo(20);
//            }];
//
//            UIView *lineView = [UIView new];
//            [backView addSubview:lineView];
//            lineView.backgroundColor = rgba(238, 238, 238, 1);
//            [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
//                make.height.mas_offset(1);
//                make.left.mas_offset(12);
//                make.bottom.mas_equalTo(backView.mas_bottom).offset(-1);
//                make.right.mas_equalTo(backView.mas_right).offset(-12);
//            }];
//
//        }
        else if (indexPath.section == 4 ) {
            for(UIView*v in _headerView.subviews) {
                [v removeFromSuperview];
            }
            if(_list_2.count) {
                _headerView.backgroundColor = [UIColor clearColor];
               
                UIView *backView = [[UIView alloc]initWithFrame:CGRectMake(12, 0, _headerView.frame.size.width-24, _headerView.frame.size.height)];
               
                [_headerView addSubview:backView];
                backView.backgroundColor = [UIColor whiteColor];
                UIBezierPath *maskPath = nil;
                CGSize size = CGSizeMake(5, 5);
                backView.layer.masksToBounds = YES;
                CGRect bound = backView.bounds ;
                
                maskPath = [UIBezierPath bezierPathWithRoundedRect:bound
                                                 byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight
                                                       cornerRadii:size];
                
                CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
                //设置大小
                maskLayer.frame = bound;
                //设置图形样子
                maskLayer.path = maskPath.CGPath;
                backView.layer.mask = maskLayer;
            }
            
        } else if ( indexPath.section == 5) {
            for(UIView*v in _headerView.subviews) {
                [v removeFromSuperview];
            }
            if(_list_3.count) {
                _headerView.backgroundColor = [UIColor clearColor];
               
                UIView *backView = [[UIView alloc]initWithFrame:CGRectMake(12, 0, _headerView.frame.size.width-24, _headerView.frame.size.height)];
               
                [_headerView addSubview:backView];
                backView.backgroundColor = [UIColor whiteColor];
                UIBezierPath *maskPath = nil;
                CGSize size = CGSizeMake(5, 5);
                backView.layer.masksToBounds = YES;
                CGRect bound = backView.bounds ;
                
                maskPath = [UIBezierPath bezierPathWithRoundedRect:bound
                                                 byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight
                                                       cornerRadii:size];
                
                CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
                //设置大小
                maskLayer.frame = bound;
                //设置图形样子
                maskLayer.path = maskPath.CGPath;
                backView.layer.mask = maskLayer;
            }
            
        }
        
        return _headerView;
    }
    UICollectionReusableView *FooterView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:@"footer" forIndexPath:indexPath];
    FooterView.backgroundColor =  rgba(245, 245, 245, 1);
    return FooterView;
}

#pragma mark -- UICollectionViewDelegate
#pragma mark -- 底部按钮点击
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    MJWeakSelf
    if(![HYTools changeUidIsNull]){
        [[CommonFunc sharedInstance] judgeUnLogin:self message:@"你还没有登录,请先登录"];
        return;
    }
    if (indexPath.section == 3 || indexPath.section == 4 || indexPath.section == 5) {
        NSDictionary *dataDic;
        if (indexPath.section == 3) {
            dataDic = self.list_1[indexPath.row];
        }
        else if (indexPath.section == 4) {
            dataDic = self.list_2[indexPath.row];
        } else{
            dataDic = self.list_3[indexPath.row];
        }
        DLog(@"%@",dataDic);
        [CommonFunc webPushAppPage:dataDic];
        [CommonFunc buriedPointRequtstWithGenre:3 channel:7 region_id:403000 button_id:[dataDic[@"id"] intValue] mid:@""];
    }else if (indexPath.section == 1){
        NSString *selectStr = _orderArray[indexPath.row][@"menuname"];
        if([selectStr isEqualToString:@"写酒评"]) {
            [CommonFunc buriedPointRequtstWithGenre:3 channel:7 region_id:701000 button_id:2 mid:@""];
            GeneralWebViewController *activeWeb=[[GeneralWebViewController alloc]init];
            activeWeb.url = HMSTR(@"%@",wineCommentUrl);
            activeWeb.isHideNav = YES;
            [self.navigationController pushViewController:activeWeb animated:YES];
            return;
        } else {
            MyOrderViewController *orderVc = [MyOrderViewController new];
            if([selectStr isEqualToString:@"待支付"]) {
                orderVc.type = indexPath.row+1;
            } else if([selectStr isEqualToString:@"全部订单"]) {
                orderVc.type = 0;
            }
            else {
                orderVc.type = indexPath.row+2;
            }
            [self.navigationController pushViewController:orderVc animated:YES];
        }
    
    }else if (indexPath.section == 2){
        if(_secondModel.show_balance ){
            if (indexPath.row == 0) {
                // 余额
                [CommonFunc buriedPointRequtstWithGenre:3 channel:7 region_id:402000 button_id:3 mid:@""];
                GeneralWebViewController *activeWeb=[[GeneralWebViewController alloc]init];
                activeWeb.url = myBalanceUrl;
                activeWeb.isHideNav = NO;
                activeWeb.isStatusBar = NO;
                [self.navigationController pushViewController:activeWeb animated:YES];
            } else if (indexPath.row == 1) {
                // 兔头 - 今日任务
                [CommonFunc buriedPointRequtstWithGenre:3 channel:7 region_id:402000 button_id:1 mid:@""];
                DailyCheckViewController *DailyBtn = [DailyCheckViewController new];
                [self.navigationController pushViewController:DailyBtn animated:YES];
            } else if (indexPath.row == 2) {
                // 优惠券
                [CommonFunc buriedPointRequtstWithGenre:3 channel:7 region_id:402000 button_id:2 mid:@""];
                MyCouponViewController *vc = [[MyCouponViewController alloc] init];
                [self.navigationController pushViewController:vc animated:YES];
            }
        } else {
            if (indexPath.row == 0) {
                //今日任务
                [CommonFunc buriedPointRequtstWithGenre:3 channel:7 region_id:402000 button_id:1 mid:@""];
                DailyCheckViewController *DailyBtn = [DailyCheckViewController new];
                [self.navigationController pushViewController:DailyBtn animated:YES];
            }else{
                [CommonFunc buriedPointRequtstWithGenre:3 channel:7 region_id:402000 button_id:2 mid:@""];
                MyCouponViewController *vc = [[MyCouponViewController alloc] init];
                [self.navigationController pushViewController:vc animated:YES];
            }
        }
       
    }
//    else if (indexPath.section == 4) {
//        if(indexPath.item == 0) {
//            if([[UserInforObject userSingleton] userInfo].is_auction_seller.intValue) {
//                 NSString *str = [NSString stringWithFormat:@"草稿箱: %ld",[[DraftBoxManager sharedManager] getTotalDraftCount]];
//                 NSMutableAttributedString *attstir = [[NSMutableAttributedString alloc] initWithString:str];
//                 [attstir addAttributes:@{NSFontAttributeName:[UIFont PingFangSemiboldofSize:12],NSForegroundColorAttributeName:[YDMyColors getMainRedColor]} range:NSMakeRange(5, str.length-5)];
//                 [self.chooseAuctionTypeView.draftBtn setAttributedTitle:attstir forState:UIControlStateNormal];
//                 TFPopupParam *param = [TFPopupParam new];
//                 param.disuseBackgroundTouchHide = YES;
//                 param.backgroundColor = rgba(3, 0, 0, 0.6);
//                 UIWindow *window = [UIApplication sharedApplication].windows[0];
//                 [self.chooseAuctionTypeView tf_showScale:window offset:CGPointMake(0, 0) popupParam:param];
//            } else {
//                _authenticationPopView = [[LotCancleLikePopView alloc] initWithFrame:CGRectMake(0, 0, SizeWidth(276), SizeWidth(207))];
//                 [_authenticationPopView.leftBtn setTitle:@"取消" forState:UIControlStateNormal];
//                 [_authenticationPopView.rightBtn setTitle:@"确定" forState:UIControlStateNormal];
//                 _authenticationPopView.titleLabel.text = @"请先进行实名认证，认证通过后可发布拍品。";
//                _authenticationPopView.titleLabel.textColor = [YDMyColors getlightTextColor];
//                 _authenticationPopView.contentLabel.text = @"";
//                 [_authenticationPopView.leftBtn addUpInside:^(UIButton *button) {
//                     [weakSelf.authenticationPopView tf_hide];
//                 }];
//                 [_authenticationPopView.rightBtn addUpInside:^(UIButton *button) {
//                      [weakSelf.authenticationPopView tf_hide];
//                      AuctionAuthenticationViewController *vc = [[AuctionAuthenticationViewController alloc] init];
//                      [weakSelf.navigationController pushViewController:vc animated:YES];
//                 }];
//                 [_authenticationPopView showAlertView];
//            }
//            
//        }
//        else if (indexPath.item == 1) {
//            MyPostedAuctionViewController *vc = [[MyPostedAuctionViewController alloc] init];
//            [self.navigationController pushViewController:vc animated:YES];
//        } else {
//            GeneralWebViewController *activeWeb=[[GeneralWebViewController alloc]init];
//            activeWeb.url = _auctionArray[indexPath.row][@"jumpLink"];
//            activeWeb.isHideNav = YES;
//            activeWeb.isStatusBar = YES;
//            [self.navigationController pushViewController:activeWeb animated:YES];
//        }
//       
//       
//    }
}

#pragma mark 在线客服
- (void)pushkefuMessage{
    if(![HYTools changeUidIsNull]){
        [[CommonFunc sharedInstance] judgeUnLogin:self message:@"你还没有登录,请先登录"];
        return;
    }
   
    [[CommonFunc sharedInstance]pushVC:@"ordinarykefu" andParams:@[]];
}

#pragma mark - 通知
- (void)mePageChange:(NSNotification *)note{
    if ([HYTools changeUidIsNull]) {
        [self refreshMyData];
    } else {
        self.currentInfo = [[UserInfo alloc] init];
        [self updateMyView];
    }
}
//更新用户信息
- (void)upUser:(NSNotification *)note{
    self.personView.nikeLab.text = [[UserInforObject userSingleton]userInfo].nickname.length == 0 ? @"酒云网用户" : [[UserInforObject userSingleton]userInfo].nickname;
    [self.personView.headImg sd_setImageWithURL:[NSURL URLWithString:[[UserInforObject userSingleton]userInfo].avatar_image] placeholderImage:[YDMyImagePath defultIconImage]];
}

//状态栏 白色
- (UIStatusBarStyle)preferredStatusBarStyle{
    return UIStatusBarStyleLightContent;
}

- (UIImage *)navigationBarShadowImage {
    return [UIImage qmui_imageWithColor:UIColorClear size:CGSizeMake(1, PixelOne) cornerRadius:0];
}

//- (void)updateBottomView:(NSNotification *)notice  {
//
//   
//    if([notice.object intValue]) {
//        self.ticketNewPeople.hidden = YES;
//    } else {
//        if(![HYTools changeUidIsNull]) {
//            self.ticketNewPeople.hidden = NO;
//            self.ticketNewPeople.isLogin = YES;
//           
//        } else {
//            UserInfo *user = [UserInforObject userSingleton].userInfo;
//            if(user.is_new_user.intValue) {
//                self.ticketNewPeople.hidden = NO;
//                self.ticketNewPeople.isLogin = NO;
//                if(user.pople_collect_status.intValue) {
//                    self.ticketNewPeople.bgImageView.image = [UIImage imageNamed:@"icon_new_people_ticket_bottom_imge"];
//                } else {
//                    self.ticketNewPeople.bgImageView.image = [UIImage imageNamed:@"icon_new_people_ticket_revice_imge"];
//                }
//            } else {
//                
//                self.ticketNewPeople.hidden = YES;
//                
//            }
//        }
//    }
//    
//}

/// 处理角标数字
/// - Parameter afferent: 传入数
- (NSInteger)handleNumberWithAfferent:(NSInteger)afferent {
    if(afferent > 99) {
        afferent = 99;
    }
    return afferent;
}


- (PublishAuctionSelectTypeView *)chooseAuctionTypeView {
    if(!_chooseAuctionTypeView) {
        MJWeakSelf
        _chooseAuctionTypeView = [[PublishAuctionSelectTypeView alloc] initWithFrame:CGRectMake(0, 0, SizeWidth(276), SizeWidth(349))];
//        [_chooseAuctionTypeView.liquorBtn addUpInside:^(UIButton *button) {
//                    
//        }];
//        [_chooseAuctionTypeView.nonalcoholicBtn addUpInside:^(UIButton *button) {
//                    
//        }];
//        [_chooseAuctionTypeView.draftBtn addUpInside:^(UIButton *button) {
//                    
//        }];
      
    }
    return _chooseAuctionTypeView;
}
@end
