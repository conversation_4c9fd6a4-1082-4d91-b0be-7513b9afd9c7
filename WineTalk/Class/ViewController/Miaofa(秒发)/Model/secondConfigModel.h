//
//  secondConfigModel.h
//  WineTalk
//
//  Created by <PERSON><PERSON><PERSON> on 2024/12/19.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface secondConfigModel : NSObject
@property (nonatomic , assign) NSInteger              isopen;
@property (nonatomic , copy) NSString              * topbg;
@property (nonatomic , copy) NSString              * lpbk;
@property (nonatomic , copy) NSString              * db;
@property (nonatomic , copy) NSString              * bgcolor;
@property (nonatomic , copy) NSString              * wztext;
@property (nonatomic , copy) NSString              * bannerdt;
@property (nonatomic , copy) NSString              * cpbk;
@property (nonatomic , copy) NSString              * wzcolor;
@property (nonatomic , copy) NSString              * centerbg;
@property (nonatomic , copy) NSString              * cpds;
@property (nonatomic , copy) NSString              * tabbarImg;
@property (nonatomic , assign) NSInteger              show_balance;
@end

NS_ASSUME_NONNULL_END
