//
//  UserInfo.h
//  sanxunManagement
//
//  Created by San<PERSON> on 14-4-1.
//  Copyright (c) 2014年 Sansu. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface UserInfo : NSObject <NSCoding>

@property (nonatomic, copy) NSString *pid;
///用户id
@property (nonatomic, copy) NSString *uid;
///手机号
@property (nonatomic, copy) NSString *telephone;
///头像url
@property (nonatomic, copy) NSString *avatar_image_url;
///头像
@property (nonatomic, copy) NSString *avatar_image;
//拼接
@property (nonatomic, copy) NSString *url;
///经验值
@property (nonatomic, assign) NSInteger exps;
@property (nonatomic, assign) NSInteger type;
///兔头数
@property (nonatomic, assign) NSInteger rabbit;
///可领兔头数
@property (nonatomic, assign) NSInteger rabbit_available;
///昵称
@property (nonatomic, copy) NSString *nickname;
///生日
@property (nonatomic, copy) NSString *birthday;
///注册时间
@property (nonatomic, copy) NSString *created_time;
///认证信息
@property (nonatomic, copy) NSString *certified_info;
///用户等级
@property (nonatomic, copy) NSString *user_level;
///用户等级称号
@property (nonatomic, copy) NSString *user_level_name;
///APP微信绑定openid
@property (nonatomic, copy) NSString *app_openid;
///性别
@property (nonatomic, assign) NSInteger sex;
///评论/点赞是否推送 0否 1是
@property (nonatomic, assign) NSInteger is_pushconbtn;
///是否推送系统消息 0否 1是(包括商品推送)
@property (nonatomic, assign) NSInteger is_pushsysbtn;
///是否推送物流消息 0否 1是
@property (nonatomic, assign) NSInteger is_pushwlbtn;
///优惠券到期是否推送 0否 1是
@property (nonatomic, assign) NSInteger is_pushcoubtn;
///收藏数
@property (nonatomic, assign) NSInteger collect_nums;
///足迹数
@property (nonatomic, assign) NSInteger footprint_nums;
///关注数
@property (nonatomic, assign) NSInteger attention_nums;
///粉丝数
@property (nonatomic, assign) NSInteger fan_nums;
///待支付数
@property (nonatomic, assign) NSInteger unpaid_nums;
///已支付数
@property (nonatomic, assign) NSInteger paid_nums;
///已发货数
@property (nonatomic, assign) NSInteger shipped_nums;
///待拼团数
@property (nonatomic, assign) NSInteger unporder_nums;
///售后
@property (nonatomic, assign) NSInteger after_sale_nums;
/// 优惠券总数
@property (nonatomic, assign) NSInteger coupon_totals;
/// 优惠券即将过期数
@property (nonatomic, assign) NSInteger conpon_expirings;

@property (nonatomic, copy) NSString *auction_credit_score;
//个性化推荐开关：0-开启，1-关闭
@property (nonatomic, copy) NSString *recommendation_push;
///首页选择：1-主页，0-现货速发首页
@property (nonatomic, copy) NSString *home_select;

///是否新用户：0-否，1-是
@property (nonatomic, copy) NSString *is_new_user;

///领取新人权益状态：0-未领取，1-已领取
@property (nonatomic, copy) NSString *pople_collect_status;

/// 拍品提醒数
@property (nonatomic, copy) NSString *auction_reminder_nums;


/// 酒瓶数
@property (nonatomic, copy) NSString *wine_comment_nums;
/// 是否拍卖卖家：0-否，1-是
@property (nonatomic, copy) NSString *is_auction_seller;


@property (nonatomic, assign) double bonus_balance;
@property (nonatomic, assign) double recharge_balance;
@end
